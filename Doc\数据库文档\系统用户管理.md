
# 系统管理数据库设计文档

[toc]

## 系统账户表(SysAdmin)

| 序号 | 列名 | 类型 | 说明 | 
|-|-|-|-|
|1|Id|bigint|Id|
|2|Account|nvarchar(32)|账号| 
|3|Password|nvarchar(512)|密码|
|4|RealName|nvarchar(32)|真实姓名|
|5|Avatar|nvarchar(512)|头像|
|6|Phone|nvarchar(11)|手机号码|
|7|Status|int|状态|
|8|Remark|nvarchar(256)|备注|
|9|AccountType|int|账号类型|
|10|ShopId|bigint|店铺Id|
|11|LastLoginIp|nvarchar(256)|最新登录Ip|
|12|LastLoginAddress|nvarchar(128)|最新登录地点|
|13|LastLoginTime|datetime|最新登录时间|
|14|LastLoginDevice|nvarchar(128)|最新登录设备|
|15|Signature|nvarchar(512)|电子签名|
|16|CreateTime|datetime|创建时间|
|17|UpdateTime|datetime|更新时间|
|18|CreateUserId|bigint|创建者Id|
|19|UpdateUserId|bigint|修改者Id|
|20|IsDelete|bit|软删除|  
|21|RoleId|bigint|角色Id|

## 系统用户角色表(SysAdminRole)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|  
|1|Id|bigint|Id|
|2|AdminId|bigint|用户Id|
|3|RoleId|bigint|角色Id|

## 系统菜单表(SysMenu)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Pid|bigint|父Id|
|3|Type|int|菜单类型|
|4|Name|nvarchar(64)|路由名称|
|5|Path|nvarchar(128)|路由地址|
|6|Component|nvarchar(128)|组件路径|
|7|Redirect|nvarchar(128)|重定向|
|8|Permission|nvarchar(128)|权限标识|
|9|Title|nvarchar(64)|菜单名称|
|10|Icon|nvarchar(128)|图标|
|11|IsIframe|bit|是否内嵌|
|12|OutLink|nvarchar(256)|外链链接|  
|13|IsHide|bit|是否隐藏|
|14|IsKeepAlive|bit|是否缓存|
|15|IsAffix|bit|是否固定|
|16|OrderNo|int|排序|
|17|Status|int|状态|
|18|Remark|nvarchar(256)|备注|
|19|CreateTime|datetime|创建时间|
|20|UpdateTime|datetime|更新时间|
|21|CreateUserId|bigint|创建者Id|
|22|UpdateUserId|bigint|修改者Id|
|23|IsDelete|bit|软删除|

## 系统角色表(SysRole)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Name|nvarchar(64)|名称|
|3|Code|nvarchar(64)|编码| 
|4|RoleType|int|角色类型|
|5|OrderNo|int|排序|
|6|DataScope|int|数据范围|
|7|Remark|nvarchar(256)|备注|
|8|Status|int|状态|
|9|CreateTime|datetime|创建时间|
|10|UpdateTime|datetime|更新时间|
|11|CreateUserId|bigint|创建者Id|
|12|UpdateUserId|bigint|修改者Id|
|13|IsDelete|bit|软删除|

## 系统角色菜单表(SysRoleMenu)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|RoleId|bigint|角色Id|
|3|MenuId|bigint|菜单Id|

## 系统短信模板表(SysSmsTemplate)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|TemplateCode|nvarchar(255)|短信模板编码|
|3|TemplateName|nvarchar(255)|短信模板名称|
|4|TemplateContent|nvarchar(255)|短信模板内容|
|5|CreateTime|datetime|创建时间|
|6|UpdateTime|datetime|更新时间| 
|7|CreateUserId|bigint|创建者Id|
|8|UpdateUserId|bigint|修改者Id|
|9|IsDelete|bit|软删除|

## 系统短信记录表(SysSmsSendLog)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Type|int|发送场景|
|3|PhoneNumber|nvarchar(255)|手机号|
|4|TemplateCode|nvarchar(255)|短信模板|  
|5|TemplateParam|nvarchar(255)|发送短信请求数据|
|6|ResponseBody|nvarchar(255)|发送短信响应数据|
|7|IsSuccess|bit|是否发送成功|
|8|CreateTime|datetime|创建时间|
|9|UpdateTime|datetime|更新时间|
|10|CreateUserId|bigint|创建者Id|
|11|UpdateUserId|bigint|修改者Id|
|12|IsDelete|bit|软删除|
