﻿
using System.Dynamic;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;
public static class IEnumerableExtensions
{
    public static IEnumerable<ExpandoObject> ShapeData<TSource>(this IEnumerable<TSource> source, string fields)
    {
        if (source == null)
        {
            throw new ArgumentException(nameof(source));
        }

        var expandoObjectList = new List<ExpandoObject>(source.Count());

        var propertyInfoList = new List<PropertyInfo>();

        if (string.IsNullOrWhiteSpace(fields))
        {
            var propertyInfos = typeof(TSource).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            propertyInfoList.AddRange(propertyInfos);
        }
        else
        {
            var fieldsAfterSplit = fields.Split(',');
            foreach (var field in fieldsAfterSplit)
            {
                var propertyName = field.Trim();
                var propertyInfo = typeof(TSource).GetProperty(propertyName,
                    BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                if (propertyInfo == null)
                {
                    throw new Exception($"Property: {propertyName} 没有找到：{typeof(TSource)}");
                }

                propertyInfoList.Add(propertyInfo);
            }
        }

        foreach (TSource obj in source)
        {
            var shapedObj = new ExpandoObject();

            foreach (var propertyInfo in propertyInfoList)
            {
                var propertyValue = propertyInfo.GetValue(obj);
                ((IDictionary<string, object>)shapedObj).Add(propertyInfo.Name, propertyValue);
            }

            expandoObjectList.Add(shapedObj);
        }

        return expandoObjectList;
    }

    public static IEnumerable<ExpandoObject> ShapeData<TSource>(this IEnumerable<TSource> source, List<string> fieldList)
    {
        if (source == null)
        {
            throw new ArgumentException(nameof(source));
        }

        var expandoObjectList = new List<ExpandoObject>(source.Count());

        var propertyInfoList = new List<PropertyInfo>();

        if (fieldList.Count == 0)
        {
            var propertyInfos = typeof(TSource).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            propertyInfoList.AddRange(propertyInfos);
        }
        else
        {
            foreach (var field in fieldList)
            {
                var propertyName = field.Trim();
                var propertyInfo = typeof(TSource).GetProperty(propertyName,
                    BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

                if (propertyInfo == null)
                {
                    throw new Exception($"Property: {propertyName} 没有找到：{typeof(TSource)}");
                }

                propertyInfoList.Add(propertyInfo);
            }
        }

        foreach (TSource obj in source)
        {
            var shapedObj = new ExpandoObject();

            foreach (var propertyInfo in propertyInfoList)
            {
                var propertyValue = propertyInfo.GetValue(obj);
                ((IDictionary<string, object>)shapedObj).Add(propertyInfo.Name, propertyValue);
            }

            expandoObjectList.Add(shapedObj);
        }

        return expandoObjectList;
    }
}
