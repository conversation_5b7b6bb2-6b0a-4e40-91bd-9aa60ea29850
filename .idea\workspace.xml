<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2e384375-3184-4b33-86ac-3dbcba882053" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Admin.Web.Core/Controllers/Course/CorporateTrainingController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Admin.Web.Core/Controllers/Course/CorporateTrainingController.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/IService/Course/ICorporateTrainingService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/IService/Course/ICorporateTrainingService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/Service/Course/CorporateTraining/CorporateTrainingService.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/Service/Course/CorporateTraining/CorporateTrainingService.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/Service/Course/CorporateTraining/Dto/CorporateTrainingExamOutput.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/Service/Course/CorporateTraining/Dto/CorporateTrainingExamOutput.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/Service/Course/CorporateTraining/Dto/CorporateTrainingInput.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Application/Service/Course/CorporateTraining/Dto/CorporateTrainingInput.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Core/Entity/OfflineCourse/OfflineInteraction.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Core/Entity/OfflineCourse/OfflineInteraction.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Web.Core/Controllers/Course/OfflineCourseController.cs" beforeDir="false" afterPath="$PROJECT_DIR$/HRCommunity.NET/HRCommunity.NET.Web.Core/Controllers/Course/OfflineCourseController.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2yvtUAfILvK3SYoYjboQqzNwAv2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "dev",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="2e384375-3184-4b33-86ac-3dbcba882053" name="更改" comment="" />
      <created>1750726889564</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750726889564</updated>
      <workItem from="1750726893641" duration="3000" />
      <workItem from="1752719823591" duration="6000" />
    </task>
    <servers />
  </component>
</project>