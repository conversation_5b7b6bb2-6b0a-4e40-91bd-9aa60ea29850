namespace HRCommunity.NET.Application.Service;

/// <summary>
/// WebOffice 预览请求输入
/// </summary>
public class WebOfficePreviewInput
{
    /// <summary>
    /// 文件ID
    /// </summary>
    [Required(ErrorMessage = "文件ID不能为空")]
    public long FileId { get; set; }

    /// <summary>
    /// 签名URL过期时间（秒），默认3600秒
    /// </summary>
    public int? ExpireTime { get; set; }

    /// <summary>
    /// 是否允许打印
    /// </summary>
    public bool? AllowPrint { get; set; }

    /// <summary>
    /// 是否允许复制
    /// </summary>
    public bool? AllowCopy { get; set; }

    /// <summary>
    /// 是否允许导出为PDF
    /// </summary>
    public bool? AllowExport { get; set; }

    /// <summary>
    /// 最大渲染页数
    /// </summary>
    public int? MaxPage { get; set; }

    /// <summary>
    /// 自定义水印配置
    /// </summary>
    public WebOfficeWatermarkInput? Watermark { get; set; }
}

/// <summary>
/// WebOffice 水印配置输入
/// </summary>
public class WebOfficeWatermarkInput
{
    /// <summary>
    /// 水印文字
    /// </summary>
    public string? Text { get; set; }

    /// <summary>
    /// 水印字号
    /// </summary>
    public int? Size { get; set; }

    /// <summary>
    /// 水印透明度（0-100）
    /// </summary>
    public int? Transparency { get; set; }

    /// <summary>
    /// 水印颜色
    /// </summary>
    public string? Color { get; set; }

    /// <summary>
    /// 水印旋转角度（0-360）
    /// </summary>
    public int? Rotate { get; set; }

    /// <summary>
    /// 水印字体
    /// </summary>
    public string? FontType { get; set; }
}

/// <summary>
/// WebOffice 预览输出
/// </summary>
public class WebOfficePreviewOutput
{
    /// <summary>
    /// 预览URL
    /// </summary>
    public string PreviewUrl { get; set; } = string.Empty;

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件格式
    /// </summary>
    public string FileFormat { get; set; } = string.Empty;

    /// <summary>
    /// URL过期时间
    /// </summary>
    public DateTime ExpireTime { get; set; }

    /// <summary>
    /// 是否支持预览
    /// </summary>
    public bool IsSupported { get; set; }

    /// <summary>
    /// 错误信息（如果不支持预览）
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 通过文件路径预览输入
/// </summary>
public class WebOfficePreviewByPathInput
{
    /// <summary>
    /// 文件路径（OSS中的完整路径）
    /// </summary>
    [Required(ErrorMessage = "文件路径不能为空")]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件名（用于显示）
    /// </summary>
    [Required(ErrorMessage = "文件名不能为空")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 签名URL过期时间（秒），默认3600秒
    /// </summary>
    public int? ExpireTime { get; set; }

    /// <summary>
    /// 是否允许打印
    /// </summary>
    public bool? AllowPrint { get; set; }

    /// <summary>
    /// 是否允许复制
    /// </summary>
    public bool? AllowCopy { get; set; }

    /// <summary>
    /// 是否允许导出为PDF
    /// </summary>
    public bool? AllowExport { get; set; }

    /// <summary>
    /// 最大渲染页数
    /// </summary>
    public int? MaxPage { get; set; }

    /// <summary>
    /// 自定义水印配置
    /// </summary>
    public WebOfficeWatermarkInput? Watermark { get; set; }
}

/// <summary>
/// WebOffice 简化预览输入（仅显示必要参数）
/// </summary>
public class WebOfficeSimplePreviewInput
{
    /// <summary>
    /// 文件路径（OSS中的完整路径）
    /// </summary>
    [Required(ErrorMessage = "文件路径不能为空")]
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件名（用于显示）
    /// </summary>
    [Required(ErrorMessage = "文件名不能为空")]
    public string FileName { get; set; } = string.Empty;
}

