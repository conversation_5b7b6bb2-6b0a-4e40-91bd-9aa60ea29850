﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 课程 - 分类服务
/// </summary>
[ApiDescriptionSettings("Course", Order = 500)]
public class CourseCateController : IDynamicApiController
{
    private readonly ICourseCateService _circleCateService;
    public CourseCateController(ICourseCateService circleCateService)
    {
        _circleCateService = circleCateService;
    }

    /// <summary>
    /// 获取课程类型下拉列表
    /// </summary>
    /// <param name="ParentId">父级id  0查询所有父级  1根据此id查询所有的子集</param>
    /// <param name="CateType"></param>
    /// <returns></returns>
    [DisplayName("获取课程类型下拉列表")]
    public async Task<List<CourseCateDropDownOutput>> GetDropDownList(int ParentId,int CateType =1)
    {
        return await _circleCateService.GetDropDownList(ParentId, CateType);
    }

    /// <summary>
    /// 获取课程类型分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取课程类型分页列表")]
    public async Task<List<CourseCategoryNode>> Page(PageCourseCateInput input)
    {
        return await _circleCateService.Page(input);
    }

    /// <summary>
    /// 增加分类
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加分类")]
    public async Task<bool> AddCate(AddCourseCateInput input)
    {
        return await _circleCateService.Add(input);
    }

    /// <summary>
    /// 更新分类
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新分类")]
    public async Task<bool> UpdateCate(UpdateCourseCateInput input)
    {
        return await _circleCateService.Update(input);
    }


    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除分类")]
    public async Task<bool> DeleteCate(DeleteCourseCateInput input)
    {
        return await _circleCateService.Delete(input);
    }

    /// <summary>
    /// 设置分类状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置分类状态")]
    public async Task<bool> SetStatus(CourseCateInput input)
    {
        return await _circleCateService.SetStatus(input);
    }
}
