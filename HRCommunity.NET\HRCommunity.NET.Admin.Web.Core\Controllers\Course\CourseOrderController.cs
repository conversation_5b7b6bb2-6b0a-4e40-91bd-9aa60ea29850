﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 课程 - 订单服务
/// </summary>
[ApiDescriptionSettings("Course", Order = 400)]
public class CourseOrderController : IDynamicApiController
{
    private readonly ICourseOrderService _courseOrderService;
    private readonly UserManager _userManager;
    public CourseOrderController(ICourseOrderService courseOrderService,
                                 UserManager userManager)
    {
        _courseOrderService = courseOrderService;
        _userManager = userManager;
    }


    /// <summary>
    /// 获取课程订单分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取课程订单分页列表")]
    public async Task<SqlSugarPagedList<PageCourseOrderOutput>> Page(PageCourseOrderInput input)
    {
        return await _courseOrderService.Page(input);
    }

    /// <summary>
    /// 获取课程订单详情
    /// </summary>
    /// <param name="OrderId"></param>
    /// <returns></returns>
    [DisplayName("获取课程订单分页列表")]
    public async Task<CourseOrderDetailOutput> GetDetail(long OrderId)
    {
        return await _courseOrderService.Detail(OrderId);
    }

    /// <summary>
    /// 课程订单退款申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程订单退款申请")]
    public async Task<bool> OrderRefundSubmit(OrderRefundInput input)
    {
        input.CreateUserId = _userManager.UserId;
        return await _courseOrderService.OrderRefund(input);
    }

    /// <summary>
    /// 获取售后记录列表
    /// </summary>
    /// <param name="OrderId"></param>
    /// <returns></returns>
    [DisplayName("获取售后记录列表")]
    public async Task<List<CourseOrderRefund>> GetOrderRefundList(long OrderId)
    {
        return await _courseOrderService.GetOrderRefundList(OrderId);
    }

    /// <summary>
    /// 导出订单数据
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportOrder"), NonUnify]
    [DisplayName("导出订单数据")]
    public async Task<IActionResult> ExportOrder(PageCourseOrderInput input)
    {
        var exportList = await _courseOrderService.GetExportList(input);

        IExcelExporter excelExporter = new ExcelExporter();
        var res = await excelExporter.ExportAsByteArray(exportList);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"订单列表.xlsx" };
    }

    /// <summary>
    /// 修改考证信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("修改考证信息")]
    [Obsolete]
    public async Task<bool> SaveCourseCert(AddCourseCertInfoInput input)
    {
        input.UpdateUserId = _userManager.UserId;
        //return await _courseOrderService.SaveCourseCert(input);
        return false;
    }

    /// <summary>
    /// 修改订单已提现
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("修改订单已提现")]
    public async Task<bool> SetCashOut(BaseIdInput input)
    {
        return await _courseOrderService.SetCashOut(input);
    }
}
