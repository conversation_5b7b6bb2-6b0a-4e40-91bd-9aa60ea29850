{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "Upload": {
    "Path": "dev/upload/{yyyy}/{MM}/{dd}", // 文件上传目录
    "MaxSize": 1048576,
    "VideoMaxSize": 5242880000, // 5G
    "ContentType": [
      "image/jpg",
      "image/png",
      "image/jpeg",
      "image/gif",
      "image/bmp",
      "text/plain",
      "application/pdf",
      "application/msword", //doc
      "application/vnd.ms-excel", //xls
      "application/vnd.ms-powerpoint", //ppt
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", //xlsx
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", //docx
      "application/vnd.openxmlformats-officedocument.presentationml.presentation", //pptx
      "video/mp4",
      "video/avi",
      "video/wmv",
      "video/mov",
      "video/flv",
      "video/rmvb",
      "video/3gp",
      "video/m4v",
      "video/mkv",
      "video/quicktime",
      "video/x-quicktime",
      "application/zip",
      "application/x-rar-compressed", //rar
      "application/octet-stream",
      "application/x-zip-compressed"
    ]
  },
  "OSSProvider": {
    "IsEnable": true,
    "Provider": "Aliyun", // Invalid/Minio/Aliyun/QCloud/Qiniu/HuaweiCloud
    "Endpoint": "oss-cn-beijing.aliyuncs.com",
    "Region": "beijing",
    "AccessKey": "LTAI5tL5WgxjAG4CexN4gQg7",
    "SecretKey": "******************************",
    "IsEnableHttps": true,
    "IsEnableCache": true,
    "Bucket": "hrshequnew"
  }
}