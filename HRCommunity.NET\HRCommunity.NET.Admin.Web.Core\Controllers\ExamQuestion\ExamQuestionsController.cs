﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 题目管理服务
/// </summary>
[ApiDescriptionSettings("试卷", Order = 500)]
public class ExamQuestionsController : IDynamicApiController
{
    private readonly IExamQuestionsService _examQuestionsService;
    private readonly IExamPaperService _examPaperService;
    public ExamQuestionsController(IExamQuestionsService examQuestionsService,
                                   IExamPaperService examPaperService)
    {
        _examQuestionsService = examQuestionsService;
        _examPaperService = examPaperService;
    }

    /// <summary>
    /// 获取题目分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取题目分页列表")]
    public async Task<SqlSugarPagedList<ExamQuestionsPageOutput>> Page(ExamQuestionsPageInput input)
    {
        return await _examQuestionsService.Page(input);
    }

    /// <summary>
    /// 增加题目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加题目")]
    public async Task<bool> AddQuestion(AddExamQuestionsInput input)
    {
        var id = await _examQuestionsService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新题目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新题目")]
    public async Task<bool> UpdateQuestion(UpdateExamQuestionsInput input)
    {
        return await _examQuestionsService.Update(input);
    }

    /// <summary>
    /// 删除题目
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除题目")]
    public async Task<bool> DeleteQuestion(BaseIdInput input)
    {
        return await _examQuestionsService.Delete(input);
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置题目状态")]
    public async Task<bool> SetStatus(ExamQuestionsStatusInput input)
    {
        return await _examQuestionsService.SetStatus(input);
    }

    /// <summary>
    /// 获取详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取题目详情")]
    public async Task<ExamQuestions> GetDetail(long id)
    {
        return await _examQuestionsService.GetDetail(id);
    }

    #region 试卷管理

    /// <summary>
    /// 获取试卷分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取试卷分页列表")]
    public async Task<SqlSugarPagedList<ExamPaperPageOutput>> PaperPage(ExamPaperPageInput input)
    {
        return await _examPaperService.Page(input);
    }

    /// <summary>
    /// 增加试卷
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "AddPaper"), HttpPost]
    [DisplayName("增加试卷")]
    public async Task<long> AddPaper(AddExamPaperInput input)
    {
        return await _examPaperService.Add(input);
    }

    /// <summary>
    /// 更新试卷
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UpdatePaper"), HttpPost]
    [DisplayName("更新试卷")]
    public async Task<bool> UpdatePaper(UpdateExamPaperInput input)
    {
        return await _examPaperService.Update(input);
    }

    /// <summary>
    /// 删除试卷
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "DeletePaper"), HttpPost]
    [DisplayName("删除试卷")]
    public async Task<bool> DeletePaper(BaseIdInput input)
    {
        return await _examPaperService.Delete(input);
    }

    /// <summary>
    /// 获取试卷详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取试卷详情")]
    public async Task<ExamPaper> GetPaperDetail(long id)
    {
        return await _examPaperService.GetDetail(id);
    }

    /// <summary>
    /// 设置试卷状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置试卷状态")]
    public async Task<bool> SetPaperStatus(ExamPaperStatusInput input)
    {
        return await _examPaperService.SetStatus(input);
    }

    /// <summary>
    /// 获取试卷下拉列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取试卷下拉列表")]
    public async Task<List<ExamPaperDropDownOutput>> GetPaperDropDownList()
    {
        return await _examPaperService.GetDropDownList();
    }

    #endregion

}
