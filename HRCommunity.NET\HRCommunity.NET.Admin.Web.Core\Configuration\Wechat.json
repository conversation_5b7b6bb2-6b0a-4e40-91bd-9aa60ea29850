{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "Wechat": {
    // 公众号
    "WechatAppId": "wx7e6acf2f39c7e37f",
    "WechatAppSecret": "81a7c762fe3c4178a6d5fdd2477f5160",
    // 小程序
    "WxOpenAppId": "wx004748211a49070f",
    "WxOpenAppSecret": "d346a56ccc8ef69dcb3ccffc2a7c4b7f"
  },
  // 微信支付
  "WechatPay": {
    "AppId": "wx7e6acf2f39c7e37f", // 微信公众平台AppId、开放平台AppId、小程序AppId、企业微信CorpId
    "MerchantId": "1484405552", // 商户平台的商户号
    "MerchantV3Secret": "63097cff6e16e16b603c22593ccbed07", // 商户平台的APIv3密钥
    "MerchantCertificateSerialNumber": "52E4F652E3EFA67BE956B5669EA509ABD706ECC5", // 商户平台的证书序列号52E4F652E3EFA67BE956B5669EA509ABD706ECC5
    //"MerchantCertificatePrivateKey": "D:\\MyWork\\Source\\HRCommunity\\Cert\\1484405552_cert\\apiclient_key.pem", // 商户平台的API证书私钥(apiclient_key.pem文件内容)
    "MerchantCertificatePrivateKey": "\\WxPayCert\\apiclient_key.pem" // 商户平台的API证书私钥(apiclient_key.pem文件内容)
  },
  // 支付回调
  "PayCallBack": {
    "WechatPayUrl": "https://xxx/api/sysWechatPay/payCallBack", // 微信支付回调
    "WechatRefundUrl": "", // 微信退款回调
    "AlipayUrl": "", // 支付宝支付回调
    "AlipayRefundUrl": "" // 支付宝退款回调
  },
  //支付宝支付配置
  "AliPay": {
    "GatewayUrl": "https://openapi.alipay.com/gateway.do", //支付宝网关（固定）。
    "AppId": "2021004109668940", // 应用APPID
    //应用私钥
    "PrivateKey": "MIIEowIBAAKCAQEAho9E1MYV24TQmjysqX64jXJuCrv0wZoSCvbUDe0EkJUYwaOWBuZ/Ju1mghMxt2t1H2q7lpfAq6YI7OyA8eIl7Iy1R3gO/288rVgjxoVlZGfU0j56VRZGx0CsOyTffFmb9rlFcFuYHgzZBtQgl5Ft6pMY1dKcVRDEz+vdlbMaXptzbVYglcUnEpNs9AHx16S48BuZ9tC++5ziUZV8SF9CjKZ9Lx+v7W2UlF4Op3j/KNLQYqCRIU1U+cI00fkghybFJK8vqVj8sEcPaB59gYFeaD7JNxH/eIMC/fDhcGR7oHzMrFEjaiQRoYX4u7BIAv85F7jcguofI5fEyTeszR1xkwIDAQABAoIBACTBC28G8D92xrfqbMsbRvOz1uTttAwRtm3yP7fSDtYjM5Doyvj1irOqUr6i2YidPG+parXWm5UHFBcK/qmTOQKk6HsuCwKwcqVylssJ8sj7J4GJ+UY2gHy6aJD7IGRJ4bUBpDrted5zafBYjtJ3sctKBJWbIoKFwwx8A5l5Q3GlpOHu8CmlhPkpKb/SK/QHkk8a2V78pIsLSwjoWawA0VQQK2M8fHf6YgXgrzsoV2cRzUvGpyAqYUF/nlE6s9AXAuO29agP6yd5GCutAGd9TX7jlmkqHAEHNZRLNIjRyfVgBE01ONGP8191KHWb+S8jQnlZBk8DedcPjXQTopxjarkCgYEAwiarjx8kvv477u6gr0A7wR4orTdN/BHhdP7XEPzVp3ef0JYcQJLHVFxURDR1Lf+QbAbO5OJcpxgrGo23Aqy7UekMCNWbbRK85Ev37+ARYUQthBCCGQihJLk+eQKxOx7rRKMT3sNpMzdIQ+C6oijffLsSvFCm2xASt7K85+EIBJcCgYEAsWzS1o6OduKsQT4VgvibB9PR8K7kmbPOTZYyD+D5hQvS0BKHn0i0NWAHFh4S5X27UKTPurTmnqNiGy8fDN3iD9iFJKOGJFpsEfU/76f2fSGI9tQJ4lv8mikOb8AN6I2q2Tu2xJ5eA567Yj4Sn9gWPtzvWeZbVh3EkXnw3s5zrmUCgYEAhFJKtr6pEqq/OjbUuNx0XUgqvPNmaRfeqxhMh71p+DslZsoH81KWG77Zh6qg+nqleZqiQ4irUQ69wdk4E9CZ0lec5iB/T7Vnm8jS8F+sOLh4tPtzT5B8E0VVeLAHDp4iBqOGlVplRoy06d4uFPr6Zk4xlLr7HW4085IUAY1zI30CgYAwbsZ3L18QcPwYqzvwDrEzR81NaFCa7IxqfY+Y6g7zFLEtf4FqJ8yZrWcB0J/T6aiScQsLqooxz+PeC9TsDD3DmZVu1PyvO8e4dzxGzZDpw5fB2UwBcJ7Z7k80jja9vr5eooBX1+tODayVOuCWpcQc0O69mbZhIashEyp/h9foSQKBgEtnBllo3T/8CISV/6JL7mILxZ1ndJe8fdFYFyLyEpgsaRu9gEzBZSMAf8j9HzltT/Ds+OEeiBbelh7sHoQhp1HAZCwQHhI+YD4viWxVab2S6oK/ubRCdB699elq38EOIHvE8m8qydg3PXujNS5P7Op9zBu4hQzBnBdF0t2jpg2U",
    //支付宝公钥
    "AlipayPublicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAj8NVifKdejYFVldG/Z+/uRkMBQQLAnoWLbz9O18BcwS5b8RuRYRH11Yu33jFu17ElQ0a/GzVR53C0ZSsZ0nFH/ivS5pui2z1+x7qVg2Y3aZiFFz2Wj6TYaYlesJctBf5scl0unTPQp0FRcI1vhh/+XNAQ25zq3CG8rpPnbssQytNYAup0uCQFGIWb2Z8z2hPDSX3ynTOr9ZGwZX5g8amK4wanO2zJxeLKmxQ4/v6gf8KbjKfrriN5w5JUr+rFtMQmqfSoknciTAI+Lt/xFwtMoED1sjs3R2EXNcaap4Qjm/1AfOdk7Zw21PONB3LGOb1LNFvcwvIMAxgwEEqMcDN9wIDAQAB",
    "Format": "json", //设置请求格式，固定值json,
    "Charset": "UTF-8", //编码集，支持 GBK/UTF-8。
    "SignType": "RSA2" //生成签名字符串所使用的签名算法类型，目前支持 RSA2。
  }
}