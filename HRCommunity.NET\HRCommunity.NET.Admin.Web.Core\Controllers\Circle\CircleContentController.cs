﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 圈子-内容服务
/// </summary>
[ApiDescriptionSettings("Circle", Order = 490)]
public class CircleContentController : IDynamicApiController
{
    private readonly ICircleContentService _circleContentService;
    private readonly ICircleCommentService _circleCommentService;
    private readonly ICircleCateService _circleCateService;
    private readonly IGrowthActionService _growthActionService;
    private readonly IMessageInteractiveService _messageService;
    private readonly ICircleGroupService _circleGroupService;
    private readonly ISysFileService _sysFileService;
    private readonly ISysCacheService _sysCacheService;
    public CircleContentController(ICircleContentService circleContentService,
                                   ICircleCommentService circleCommentService,
                                   ICircleCateService circleCateService,
                                   IGrowthActionService growthActionService,
                                   IMessageInteractiveService messageService,
                                   ICircleGroupService circleGroupService,
                                   ISysFileService sysFileService,
                                   ISysCacheService sysCacheService)
    {
        _circleContentService = circleContentService;
        _circleCommentService = circleCommentService;
        _circleCateService = circleCateService;
        _growthActionService = growthActionService;
        _messageService = messageService;
        _circleGroupService = circleGroupService;
        _sysFileService = sysFileService;
        _sysCacheService = sysCacheService;
    }


    /// <summary>
    /// 获取圈子内容分页列表-弹框选择使用
    /// update:新增课程数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取圈子内容分页列表-弹框选择使用")]
    public async Task<SqlSugarPagedList<PageCircleDialogOutput>> PageDialog(PageCircleDialogInput input)
    {
        var ret = await _circleContentService.PageDialog(input);
        return ret;
    }

    /// <summary>
    /// 获取圈子内容分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取圈子类型分页列表")]
    public async Task<SqlSugarPagedList<PageCircleContentOutput>> Page(PageCircleContentInput input)
    {
        var ret = await _circleContentService.Page(input);
        if (ret.Items.Any(x => x.CircleGroupId > 0))
        {
            var groupList = await _circleGroupService.GetDropDownList();
            ret.Items.ForEach(x =>
            {
                if (x.CircleGroupId > 0)
                {
                    x.GroupName = groupList.Where(g => g.Id == x.CircleGroupId).FirstOrDefault()?.Name;
                }
            });
        }
        if (string.IsNullOrEmpty(input.Order))
        {
            ret.Items = ret.Items.OrderByDescending(x => x.CreateTime);
        }
        return ret;
    }


    /// <summary>
    /// 导出内容数据
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportCircle"), NonUnify]
    [DisplayName("导出内容数据")]
    public async Task<IActionResult> ExportCircle(PageCircleContentInput input)
    {

        input.Page = 1;
        input.PageSize = int.MaxValue;
        var logOpList = await _circleContentService.Page(input);
        var groupList = await _circleGroupService.GetDropDownList();
        var cateList = await _circleCateService.GetDropDownList();

        // 将数据映射到DTO
        var dtoList = logOpList.Items.Select(item =>
        {
            var basicDataList = _circleContentService.PageBasicDataList(new PageDataListInput() { Id = item.Id, Page = 1, PageSize = int.MaxValue, StartTime = DateTime.Now.AddYears(-10), EndTime = DateTime.Now }, false).Result.Items;
            var interactDataList = _circleContentService.PageInteractDataList(new PageDataListInput() { Id = item.Id, Page = 1, PageSize = int.MaxValue, StartTime = DateTime.Now.AddYears(-10), EndTime = DateTime.Now }, false).Result.Items;

            var clicks = basicDataList.Sum(v => v.Clicks);
            var relation = interactDataList.Sum(v => v.Relation);
            var comment = interactDataList.Sum(v => v.Comment);
            var commentUser = interactDataList.Sum(v => v.CommentUser);
            var visitorViews = basicDataList.Sum(v => v.VisitorsViews);
            var visitorClicks = basicDataList.Sum(v => v.VisitorsClicks);

            return new PageCircleContentOutputDto
            {
                OutId = item.OutId,
                //UserOutId = item.UserOutId,
                GroupName = groupList.Where(g => g.Id == item.CircleGroupId).FirstOrDefault()?.Name,
                NickName = item.NickName,
                Type = item.Type == CircleTypeEnum.Short ? "动态" : (item.Type == CircleTypeEnum.Long ? "经验" : "提问"),
                CateIdListString = string.Join(",", cateList.Where(v => item.CateIdList.Contains(v.Id)).Select(v => v.Name)),
                Title = item?.Title,
                Views = item.Views,
                Clicks = clicks,
                Sraise = item.Sraise,
                Share = item.Share,
                Collect = item.Collect,
                Relation = relation,
                Comment = comment,
                CommentUser = commentUser,
                interact = item.Collect + relation + item.Sraise + comment,
                exposureRate = item.Views == 0 ? 0 : Math.Round((double)clicks / item.Views, 2),
                visitorViews = visitorViews,
                visitorClicks = visitorClicks,
                visitorRate = visitorViews == 0 ? 0 : Math.Round((double)visitorClicks / visitorViews, 2),
                CreateTime = item.CreateTime.Value
            };
        }).ToList();

        IExcelExporter excelExporter = new ExcelExporter();
        var res = await excelExporter.ExportAsByteArray(dtoList);

        var memoryStream = new MemoryStream(res);

        var byteContent = memoryStream.ToArray();

        var result = new FileContentResult(byteContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        {
            FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + ".xlsx"
        };
        return result;

        //return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + "内容数据.xlsx" };
    }

    /// <summary>
    /// 增加内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加圈子内容")]
    public async Task<bool> AddContent(AddCircleContentInput input)
    {
        input.SourceType = SourceTypeEnum.Background;
        var id = await _circleContentService.Add(input);
        if (id > 0)
        {
            //自动加入圈子
            if (input.CircleGroupId.HasValue && input.CircleGroupId.Value > 0)
            {
                await _circleGroupService.JoinGroup(input.CircleGroupId.Value);
            }
        }
        return id > 0;
    }

    /// <summary>
    /// 更新内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新圈子内容")]
    public async Task<bool> UpdateContent(UpdateCircleContentInput input)
    {
        input.SourceType = SourceTypeEnum.Background;
        return await _circleContentService.Update(input);
    }

    /// <summary>
    /// 增加提问
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "AddQuestion"), HttpPost]
    [DisplayName("增加提问")]
    public async Task<bool> AddQuestion(AddCircleContentInput input)
    {
        input.Status = StatusEnum.Enable;
        input.SourceType = SourceTypeEnum.Background;
        var id = await _circleContentService.AddQuestion(input);
        if (id > 0)
        {
            //自动加入圈子
            if (input.CircleGroupId.HasValue && input.CircleGroupId.Value > 0)
            {
                await _circleGroupService.JoinGroup(input.CircleGroupId.Value);
            }
            //发送给@的人
            if (input.RemindList != null && input.RemindList.Count > 0)
            {
                await _messageService.AddMsg(new AddMsgInput()
                {
                    CircleId = id,
                    Type = 4,
                    UserIdList = input.RemindList.Distinct().Select(str => str).ToList(),
                });
            }
        }
        return id > 0;
    }

    /// <summary>
    /// 更新提问
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UpdateQuestion"), HttpPost]
    [DisplayName("更新提问")]
    public async Task<bool> UpdateQuestion(UpdateCircleContentInput input)
    {
        input.SourceType = SourceTypeEnum.Background;
        return await _circleContentService.UpdateQuestion(input);
    }


    /// <summary>
    /// 删除内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除圈子内容")]
    public async Task<bool> DeleteContent(CircleContentInput input)
    {
        return await _circleContentService.Delete(input);
    }

    /// <summary>
    /// 设置内容状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置分类状态")]
    public async Task<bool> SetStatus(StatusCircleContentInput input)
    {
        return await _circleContentService.SetStatus(input);
    }

    /// <summary>
    /// 设置内容精选
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置内容精选")]
    public async Task<bool> SetPicked(StatusCircleContentInput input)
    {
        var ret = await _circleContentService.SetPicked(input);
        //设置精选，完成每日任务
        if (ret && input.Status == StatusEnum.Enable)
        {
            await Task.Run(async () =>
            {
                var detail = await _circleContentService.GetDetail(new CircleContentInput() { Id = input.Id });
                if (detail.Type == CircleTypeEnum.Short)
                {
                    //发布动态获得加精-每日任务
                    await _growthActionService.GiveGrowth(GrowthActionEnum.CirclePicked, input.Id, detail.UserId);
                }
                else if (detail.Type == CircleTypeEnum.Quiz)
                {
                    //发布提问获得加精-每日任务
                    await _growthActionService.GiveGrowth(GrowthActionEnum.QuestionPicked, input.Id, detail.UserId);
                }
            });
        }
        return ret;
    }

    /// <summary>
    /// 圈子内容审核通过
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("圈子内容审核通过")]
    public async Task<bool> SetAuditStatusPass(AuditStatusCircleContentInput input)
    {
        return await _circleContentService.SetAuditStatus(input);
    }

    /// <summary>
    /// 获取圈子内容详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取圈子内容详情")]
    public async Task<CircleContent> Detail(CircleContentInput input)
    {
        return await _circleContentService.GetDetail(input);
    }
    /// <summary>
    /// 获取简历文件列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取简历文件列表")]
    public async Task<SqlSugarPagedList<CircleContentResume>> ResumePage(PageCircleResumeInput input)
    {
        var ret = await _circleContentService.ResumePage(input);
        return ret;
    }

    /// <summary>
    /// 下载文件(文件流)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("下载文件(文件流)")]
    public async Task<IActionResult> DownloadFileBatch(BaseIdInput input)
    {
        var list = await _circleContentService.ResumeList(input);

        var fileList = list.Select(x => new DownLoadFileInput()
        {
            FilePath = x.OssUrl,
            FileName = x.OriginalFileName,
            Suffix = x.Suffix
        }).ToList();

        return await _sysFileService.DownloadFileBatch(fileList);
    }

    /// <summary>
    /// 文件预览
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("文件预览")]
    public async Task<object> FilePreview(DownLoadFileInput input)
    {
        var cacheKey = $"{CacheConst.KeyResumePreview}{MD5Encryption.Encrypt(input.FilePath)}";

        var keyExist = _sysCacheService.ExistKey(cacheKey);
        if (!keyExist)
        {
            var ret = AlibabaCloudImm.GenerateWebofficeTokenWithOptions(input.FilePath);

            var ts = DateTime.Now.AddMinutes(28) - DateTime.Now;
            _sysCacheService.Set(cacheKey, JSON.Serialize(ret), ts);
            return ret;
        }
        else
        {
            var cacheValue = JObject.Parse(_sysCacheService.Get<string>(cacheKey));
            return cacheValue;
        }
    }


    ///// <summary>
    ///// 内容详情数据-数据概览
    ///// </summary>
    ///// <param name="id"></param>
    ///// <returns></returns>
    //[DisplayName("内容详情数据-数据概览")]
    //public async Task<CircleDetailOverviewOutput> GetDetailOverview(long id)
    //{
    //    return await _circleContentService.GetOverview(id);
    //}

    /// <summary>
    /// 内容详情-获取圈子一级评论数据列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取圈子一级评论数据列表")]
    public async Task<SqlSugarPagedList<CircleCommentOutput>> FirstCommentPage(PageCircleCommentInput input)
    {
        var ret = await _circleCommentService.Page(input);

        foreach (var item in ret.Items)
        {
            var index = ret.Items.ToList().IndexOf(item);
            item.Num = (input.Page - 1) * input.PageSize + index + 1;
        }
        return ret;
    }

    /// <summary>
    /// 内容详情-获取圈子二级评论数据列表
    /// </summary>
    /// <param name="CommentId"></param>
    /// <returns></returns>
    [DisplayName("获取圈子二级评论数据列表")]
    public async Task<List<CircleCommentOutput>> GetSecondCommentList(long CommentId = 0)
    {
        return await _circleCommentService.GetSecondList(CommentId);
    }

    /// <summary>
    /// 互动数据-总览
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("互动数据-总览")]
    public async Task<DataOverviewOutput> GetInteractDataOverview(long id)
    {
        return await _circleContentService.GetInteractDataOverview(id);
    }

    /// <summary>
    /// 互动数据-统计列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("互动数据-统计列表")]
    public async Task<SqlSugarPagedList<PageBaseDataListOutput>> PageInteractDataList(PageDataListInput input)
    {
        return await _circleContentService.PageInteractDataList(input);
    }

    /// <summary>
    /// 基础数据-统计列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("基础数据-统计列表")]
    public async Task<SqlSugarPagedList<PageBasicDataListOutput>> PageBasicDataList(PageDataListInput input)
    {
        return await _circleContentService.PageBasicDataList(input);
    }

    ///// <summary>
    ///// 盖楼发中奖短信
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[DisplayName("盖楼发中奖短信")]
    //[AllowAnonymous]
    //public void SendSmsBatch(TempSmsInput input)
    //{
    //    if (input.PhoneNumbers != null && input.PhoneNumbers.Any())
    //    {
    //        var PhoneNumbers = input.PhoneNumbers.ToList();
    //        PhoneNumbers.Add("17788210502");
    //        //发盖楼-幸运楼层充值1000诺币短信
    //        AlibabaCloudSms.SendBatchMessage(PhoneNumbers.ToArray(), "SMS_464776403", "");
    //    }
    //}

}
public class TempSmsInput
{
    public string[] PhoneNumbers { get; set; }
}