﻿using static SKIT.FlurlHttpClient.Wechat.Api.Models.ComponentTCBBatchGetEnvironmentIdResponse.Types;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 课程 - 资源
/// </summary>
[ApiDescriptionSettings("Course", Order = 491)]
public class CourseResourceController : IDynamicApiController
{
    private readonly ICourseResourceService _courseContentService;
    private readonly ISysFileService _sysFileService;
    public CourseResourceController(ICourseResourceService courseContentService, ISysFileService sysFileService)
    {
        _courseContentService = courseContentService;
        _sysFileService = sysFileService;
    }


    /// <summary>
    /// 获取资源资源分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取资源类型分页列表")]
    public async Task<SqlSugarPagedList<PageCourseResourceOutput>> Page(PageCourseResourceInput input)
    {
        return await _courseContentService.Page(input);
    }


    ///// <summary>
    ///// 导出内容数据
    ///// </summary>
    ///// <returns></returns>
    //[ApiDescriptionSettings(Name = "ExportCourse"), NonUnify]
    //[DisplayName("导出内容数据")]
    //public async Task<IActionResult> ExportCourse(PageCourseContentInput input)
    //{

    //    input.Page = 1;
    //    input.PageSize = int.MaxValue;
    //    var logOpList = await _courseContentService.Page(input);


    //    // 将数据映射到DTO
    //    var dtoList = logOpList.Items.Select(item =>
    //    {
    //        var basicDataList = _courseContentService.PageBasicDataList(new PageDataListInput() { Id = item.Id, Page = 1, PageSize = int.MaxValue, StartTime = DateTime.Now.AddYears(-10), EndTime = DateTime.Now }, false).Result.Items;
    //        var interactDataList = _courseContentService.PageInteractDataList(new PageDataListInput() { Id = item.Id, Page = 1, PageSize = int.MaxValue, StartTime = DateTime.Now.AddYears(-10), EndTime = DateTime.Now }, false).Result.Items;

    //        var clicks = basicDataList.Sum(v => v.Clicks);
    //        var relation = interactDataList.Sum(v => v.Relation);
    //        var comment = interactDataList.Sum(v => v.Comment);
    //        var commentUser = interactDataList.Sum(v => v.CommentUser);
    //        var visitorViews = basicDataList.Sum(v => v.VisitorsViews);
    //        var visitorClicks = basicDataList.Sum(v => v.VisitorsClicks);

    //        return new PageCourseContentOutputDto
    //        {
    //            OutId = item.OutId,
    //            UserOutId = item.UserOutId,
    //            NickName = item.NickName,
    //            Type = item.Type == CourseTypeEnum.Short ? "动态" : "经验",
    //            //CateIdListString = string.Join(",", _courseCateService.GetDropDownList().Result.Where(v => item.CateIdList.Contains(v.Id)).Select(v => v.Name)),
    //            Title = item?.Title,
    //            Views = item.Views,
    //            Clicks = clicks,
    //            Sraise = item.Sraise,
    //            Collect = item.Collect,
    //            Relation = relation,
    //            Comment = comment,
    //            CommentUser = commentUser,
    //            interact = item.Collect + relation + item.Sraise + comment,
    //            exposureRate = item.Views == 0 ? 0 : Math.Round((double)clicks / item.Views, 2),
    //            visitorViews = visitorViews,
    //            visitorClicks = visitorClicks,
    //            visitorRate = visitorViews == 0 ? 0 : Math.Round((double)visitorClicks / visitorViews, 2),
    //        };
    //    }).ToList();

    //    IExcelExporter excelExporter = new ExcelExporter();
    //    var res = await excelExporter.ExportAsByteArray(dtoList);

    //    var memoryStream = new MemoryStream(res);

    //    var byteContent = memoryStream.ToArray();

    //    var result = new FileContentResult(byteContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    //    {
    //        FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + ".xlsx"
    //    };
    //    return result;

    //    //return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + "内容数据.xlsx" };
    //}

    /// <summary>
    /// 增加资源内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加资源内容")]
    public async Task<bool> AddContent(AddCourseResourceInput input)
    {
        var id = await _courseContentService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新资源内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新资源内容")]
    public async Task<bool> UpdateContent(UpdateCourseResourceInput input)
    {
        return await _courseContentService.Update(input);
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task<bool> SetStatus(StatusCourseContentInput input)
    {
        return await _courseContentService.SetStatus(input);
    }

    /// <summary>
    /// 获取资源内容详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取资源内容详情")]
    public async Task<CourseResource> Detail(CourseContentInput input)
    {
        return await _courseContentService.GetDetail(input);
    }


    /// <summary>
    /// 上传资源
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "UploadResource"), HttpPost]
    [DisplayName("上传资源")]
    [RequestFormLimits(MultipartBodyLengthLimit = 5242880000)] // 设置最大上传文件为5G
    [RequestSizeLimit(5242880000)] // 设置最大请求体大小为5G
    public FileOutput UploadResource(IFormFile file)
    {
        var path = "CourseResource";
        //return await _sysFileService.UploadFile(file, path);
        return _sysFileService.UploadVideoFile(file, path);
    }

    [ApiDescriptionSettings(Name = "UploadResourceWithProgress"), HttpPost]
    [DisplayName("上传资源（进度展示）")]
    [RequestFormLimits(MultipartBodyLengthLimit = 5242880000)] // 设置最大上传文件为5G
    [RequestSizeLimit(5242880000)] // 设置最大请求体大小为5G
    public async Task UploadResourceWithProgress(IFormFile file)
    {
        var path = "CourseResource";
        await _sysFileService.UploadVideoFileWithProgress(file, path);
    }
}
