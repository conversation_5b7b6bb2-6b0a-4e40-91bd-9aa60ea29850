﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 圈子-分类服务
/// </summary>
[ApiDescriptionSettings("Circle", Order = 500)]
public class CircleCateController : IDynamicApiController
{
    private readonly ICircleCateService _circleCateService;
    public CircleCateController(ICircleCateService circleCateService)
    {
        _circleCateService = circleCateService;
    }
    /// <summary>
    /// 获取圈子类型下拉列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取圈子类型下拉列表")]
    public async Task<List<CircleCateDropDownOutput>> GetDropDownList()
    {
        return await _circleCateService.GetDropDownList();
    }

    /// <summary>
    /// 获取圈子类型分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取圈子类型分页列表")]
    public async Task<SqlSugarPagedList<CircleCategory>> Page(PageCircleCateInput input)
    {
        return await _circleCateService.Page(input);
    }

    /// <summary>
    /// 增加分类
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加分类")]
    public async Task<bool> AddCate(AddCircleCateInput input)
    {
        return await _circleCateService.Add(input);
    }

    /// <summary>
    /// 更新分类
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新分类")]
    public async Task<bool> UpdateCate(UpdateCircleCateInput input)
    {
        return await _circleCateService.Update(input);
    }


    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除分类")]
    public async Task<bool> DeleteCate(DeleteCircleCateInput input)
    {
        return await _circleCateService.Delete(input);
    }

    /// <summary>
    /// 设置分类状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置分类状态")]
    public async Task<bool> SetStatus(CircleCateInput input)
    {
        return await _circleCateService.SetStatus(input);
    }
}
