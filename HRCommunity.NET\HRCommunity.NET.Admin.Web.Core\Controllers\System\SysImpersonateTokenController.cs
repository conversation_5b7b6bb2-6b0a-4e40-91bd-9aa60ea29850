using Microsoft.AspNetCore.Authorization;

namespace HRCommunity.NET.Admin.Web.Core;

/// <summary>
/// 运营后台伪登录Token管理
/// </summary>
[ApiDescriptionSettings("System", Order = 200)]
[Route("api/[controller]")]
public class SysImpersonateTokenController : IDynamicApiController
{
    private readonly ISysImpersonateTokenService _impersonateTokenService;

    public SysImpersonateTokenController(ISysImpersonateTokenService impersonateTokenService)
    {
        _impersonateTokenService = impersonateTokenService;
    }

    /// <summary>
    /// 生成伪登录Token
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("generate")]
    [DisplayName("生成伪登录Token")]
    public async Task<ImpersonateTokenOutput> GenerateToken(GenerateImpersonateTokenInput input)
    {
        return await _impersonateTokenService.GenerateTokenAsync(input);
    }

    /// <summary>
    /// 分页查询伪登录Token
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("page")]
    [DisplayName("分页查询伪登录Token")]
    public async Task<SqlSugarPagedList<ImpersonateTokenOutput>> GetPage(ImpersonateTokenPageInput input)
    {
        return await _impersonateTokenService.GetPageAsync(input);
    }

    /// <summary>
    /// 获取伪登录Token详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id}")]
    [DisplayName("获取伪登录Token详情")]
    public async Task<ImpersonateTokenOutput> GetDetail(long id)
    {
        return await _impersonateTokenService.GetDetailAsync(id);
    }

    /// <summary>
    /// 删除伪登录Token
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("{id}")]
    [DisplayName("删除伪登录Token")]
    public async Task Delete(long id)
    {
        await _impersonateTokenService.DeleteAsync(id);
    }
}
