﻿
using Furion.DatabaseAccessor;
using Furion.Logging.Extensions;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;
/// <summary>
/// 考证课程 - 认证管理服务
/// </summary>
[ApiDescriptionSettings("考证课-认证管理", Order = 400)]
public class CourseCertController : IDynamicApiController
{
    private readonly ICourseCertService _courseCertService;
    private readonly ISysFileService _sysFileService;
    private readonly ISysSmsTemplateService _sysSmsTemplateService;

    public CourseCertController(ICourseCertService courseCertService,
                                ISysFileService sysFileService,
                                ISysSmsTemplateService sysSmsTemplateService)
    {
        _courseCertService = courseCertService;
        _sysFileService = sysFileService;
        _sysSmsTemplateService = sysSmsTemplateService;
    }

    /// <summary>
    /// 获取认证课程下拉列表
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    [DisplayName("获取认证课程下拉列表")]
    public async Task<List<CertCourseIdNameOutput>> GetCertCourseList([FromQuery]string name = "")
    {
         return await _courseCertService.GetCertCourseList(name);
    }

    /// <summary>
    /// 获取认证管理分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取认证管理分页列表")]
    public async Task<SqlSugarPagedList<PageCourseCertOutput>> Page(PageCourseCertInput input)
    {
        return await _courseCertService.Page(input);
    }

    /// <summary>
    /// 获取认证管理详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取认证管理详情")]
    public async Task<CourseCertDetailOutput> Detail(BaseIdInput input)
    {
        return await _courseCertService.GetDetail(input.Id);
    }

    /// <summary>
    /// 保存认证信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("保存认证信息")]
    public async Task<bool> Save(SaveCourseCertInfoInput input)
    {
        return await _courseCertService.SaveCert(input);
    }

    /// <summary>
    /// 审核
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("审核")]
    public async Task<bool> AuditStatus(CourseCertStatusInput input)
    {
        var auditStatus = new int[] { 1, 2, 3 };
        if (!auditStatus.Contains(input.Status))
            throw Oops.Oh("审核状态错误");
        if (input.Status == 1)
        {
            if (string.IsNullOrWhiteSpace(input.Remark))
                throw Oops.Oh("审核驳回需要填写备注");
            else
                input.LogRemark = "审核驳回 备注：" + input.Remark;
        }
        if (input.Status == 2)
        {
            if (string.IsNullOrWhiteSpace(input.Remark))
                throw Oops.Oh("审核拒绝需要填写备注");
            else
                input.LogRemark = "审核拒绝 备注：" + input.Remark;
        }
        if (input.Status == 3)
        {
            if (string.IsNullOrWhiteSpace(input.Remark))
                input.Remark = "无";
             input.LogRemark = "审核通过，待发准考证 备注：" + input.Remark;
        }
        var dto = await _courseCertService.GetDetail(input.Id);
        if (dto.Status != 0)
            throw Oops.Oh("该认证信息不是待审核状态");

        var flag = await _courseCertService.SetStatus(input);
        //如果审核通过，合并身份证图片生成PDF
        if (flag)
        {
            if (input.Status == 3) //审核通过，待发准考证
            {
                var icInput = new CourseCertIDCardUrlInput()
                {
                    Id = input.Id,
                };
                var update = false;
                if (!string.IsNullOrWhiteSpace(dto.IDCardFrontUrl) && !string.IsNullOrWhiteSpace(dto.IDCardBackUrl))
                {
                    var fileList = new List<DownLoadFileInput>
                {
                    new() { FilePath = dto.IDCardFrontUrl, FileName = "身份证人像面", Suffix = dto.IDCardFrontUrl },
                    new() { FilePath = dto.IDCardBackUrl, FileName = "身份证国徽面", Suffix = dto.IDCardBackUrl }
                };
                    //将身份证合并转换成PDF
                    if (fileList.Any())
                    {
                        ($"开始转换身份证-{DateTime.Now}").LogWarning();
                        var sysFile = await _sysFileService.ImageConvertToPdf(fileList, "身份证");
                        if (sysFile != null)
                        {
                            icInput.IDCardUrl = sysFile.FilePath + "/" + sysFile.Id + sysFile.Suffix;
                            update = true;
                        }
                        ($"结束转换身份证-{DateTime.Now}").LogWarning();
                    }
                }
                if (!string.IsNullOrWhiteSpace(dto.DiplomaPhotoUrl))
                {
                    var fileList = new List<DownLoadFileInput>
                {
                    new() { FilePath = dto.DiplomaPhotoUrl, FileName = "毕业证", Suffix = dto.DiplomaPhotoUrl },
                };
                    //将毕业证转换成PDF
                    if (fileList.Any())
                    {
                        ($"开始转换毕业证-{DateTime.Now}").LogWarning();
                        var sysFile = await _sysFileService.ImageConvertToPdf(fileList, "毕业证");
                        if (sysFile != null)
                        {
                            icInput.DiplomaPdfUrl = sysFile.FilePath + "/" + sysFile.Id + sysFile.Suffix;
                            update = true;
                        }
                        ($"结束转换毕业证-{DateTime.Now}").LogWarning();
                    }
                }
                //更新身份证和毕业证pdf
                if (update)
                {
                    await _courseCertService.UpdateCertPDFUrl(icInput);
                }
            }
            //发送短信通知
            var templateCode = input.Status == 1 ?
            SmsTemplateCodeEnum.报名资料审核驳回通知 : (input.Status == 2 ? SmsTemplateCodeEnum.报名资料审核拒绝通知 : SmsTemplateCodeEnum.报名资料审核通过通知);
            await Task.Run(async () =>
            {
                await SendSmsRequest(input.Id, templateCode);
            });
        }
        return flag;
    }

    /// <summary>
    /// 更新考试状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新考试状态")]
    public async Task<bool> ExamStatus(CourseCertStatusInput input)
    {
        var auditStatus = new int[] { 5, 6, 7 };
        if (!auditStatus.Contains(input.Status))
            throw Oops.Oh("考试状态错误");
        if (input.Status == 5)
        {
            input.LogRemark = "更新考试状态 考试状态：考试不通过";
        }
        if (input.Status == 6)
        {
            input.LogRemark = "更新考试状态 考试状态：考试缺考不通过";
        }
        if (input.Status == 7)
        {
            input.LogRemark = "更新考试状态 考试状态：考试通过（待领证）";
        }
        var dto = await _courseCertService.GetDetail(input.Id);
        if (dto.Status != 4 && dto.Status != 5)
            throw Oops.Oh("该认证信息不是待考试或者考试不通过状态");

        var ret = await _courseCertService.SetStatus(input);
        if (ret) //发短信通知
        {
            var templateCode = input.Status == 7 ?
            SmsTemplateCodeEnum.考试通过通知 :
            SmsTemplateCodeEnum.考试未通过通知;
            await Task.Run(async () =>
            {
                await SendSmsRequest(input.Id, templateCode);
            });
        }
        return ret;
    }

    /// <summary>
    /// 更新证书领取状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新证书领取状态")]
    public async Task<bool> CertReceiveStatus(CourseCertStatusInput input)
    {
        var auditStatus = new int[] { 7, 8 };
        if (!auditStatus.Contains(input.Status))
            throw Oops.Oh("证书领取状态错误");
        var dto = await _courseCertService.GetDetail(input.Id);
        if (input.Status == 7)
        {
            if (dto.Status != 8)
                throw Oops.Oh("该认证信息不是已领证状态");
            input.LogRemark = "更新证书领取状态 证书领取状态：待领证 备注：" + input.Remark;
        }
        if (input.Status == 8)
        {
            if (dto.Status != 7)
                throw Oops.Oh("该认证信息不是待领证状态");
            input.LogRemark = "更新证书领取状态 证书领取状态：已领证 备注：" + input.Remark;
        }

        var ret = await _courseCertService.SetStatus(input);
        if (ret)
        {
            //更新证书领取状态的备注
            await _courseCertService.UpdateCertReceiveRemark(input);
        }
        return ret;
    }

    /// <summary>
    /// 获取操作历史列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取操作历史列表")]
    public async Task<List<CourseCertLog>> LogList(BaseIdInput input)
    {
        return await _courseCertService.GetLogList(input.Id);
    }

    /// <summary>
    /// 打包下载下载报名资料(文件流)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("下载报名资料(文件流)")]
    public async Task<IActionResult> Download(PageCourseCertInput input)
    {
        var list = await _courseCertService.GetList(input);

        var fileList = new List<DownLoadFileInput>();
        foreach (var item in list)
        {
            var dirName = $"{item.CourseName}/{item.Name}{item.Phone}/";
            //身份证
            if (!string.IsNullOrWhiteSpace(item.IDCardUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.IDCardUrl, $"{dirName}身份证"));
            }
            //养老保险最新社保缴费证明PDF版
            if (!string.IsNullOrWhiteSpace(item.SocialUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.SocialUrl, $"{dirName}社会保险人员参保证明-养老"));
            }
            //失业保险最新社保缴费证明PDF版
            if (!string.IsNullOrWhiteSpace(item.LoseUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.LoseUrl, $"{dirName}社会保险人员参保证明-失业"));
            }
            //小二寸免冠证件照
            if (!string.IsNullOrWhiteSpace(item.IdPhotoUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.IdPhotoUrl, $"{dirName}{item.Name}{item.IDCard}证件照"));
            }
            //学信网学历电子注册备案表
            if (!string.IsNullOrWhiteSpace(item.ChsiEleUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.ChsiEleUrl, $"{dirName}教育部学历证书电子注册备案表"));
            }
            //毕业证原件视频
            if (!string.IsNullOrWhiteSpace(item.DiplomaVideoUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.DiplomaVideoUrl, $"{dirName}毕业证视频"));
            }
            //毕业证彩色扫描件
            if (!string.IsNullOrWhiteSpace(item.DiplomaPdfUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.DiplomaPdfUrl, $"{dirName}毕业证扫描件"));
            }
        }
        if(fileList.Count == 0)
        {
            throw Oops.Oh("没有可下载的文件");
        }
        return await _sysFileService.DownloadFileBatch(fileList);
    }
    private static DownLoadFileInput ConvertDownLoadFile(string filePath, string fileName)
    {
        return new DownLoadFileInput()
        {
            FilePath = filePath,
            FileName = fileName,
            Suffix = Path.GetExtension(filePath)
        };
    }
    /// <summary>
    /// 导出
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Export"), NonUnify]
    [DisplayName("导出")]
    public async Task<IActionResult> Export(PageCourseCertInput input)
    {
        var list = await _courseCertService.GetList(input);

        var ret = list.Adapt<List<ExportCourseCertDto>>();

        var excelExporter = new ExcelExporter();

        var res = await excelExporter.ExportAsByteArray(ret);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"认证管理列表导出.xlsx" };
    }

    /// <summary>
    /// 批量导入上传文件
    /// </summary>
    /// <param name="file"></param>
    /// <returns></returns>
    [UnitOfWork]
    [DisplayName("批量导入上传文件")]
    public async Task<CertBatchResultExportOutput> UploadFile([Required] IFormFile file)
    {
        var ret = await _courseCertService.UploadFile(file);

        return ret;
    }
    /// <summary>
    /// 认证相关发短信封装
    /// </summary>
    /// <param name="id"></param>
    /// <param name="templateCode"></param>
    /// <returns></returns>
    private async Task SendSmsRequest(long id,SmsTemplateCodeEnum templateCode)
    {
        //查询认证信息
        var certUserInfo = await _courseCertService.GetDetail(id) ?? throw Oops.Oh("信息不存在");
        //查询模板
        var temp = await _sysSmsTemplateService.GetInfo((int)templateCode);

        var smsLog = new SysSmsSendLog
        {
            PhoneNumber = certUserInfo.Phone,
            TemplateCode = temp.TemplateCode,
            TemplateParam = JSON.Serialize(new
            {
                certificate = certUserInfo.CourseInfo.CourseName
            })
        };
        switch (templateCode)
        {
            case SmsTemplateCodeEnum.报名资料审核拒绝通知:
                smsLog.Type = SmsSendTypeEnum.CertNoPass;
                break;
            case SmsTemplateCodeEnum.报名资料审核通过通知:
                smsLog.Type = SmsSendTypeEnum.CertPass;
                break;
            case SmsTemplateCodeEnum.报名资料审核驳回通知:
                smsLog.Type = SmsSendTypeEnum.CertBoHui;
                break;
            case SmsTemplateCodeEnum.考试未通过通知:
                smsLog.Type = SmsSendTypeEnum.ExamNoPass;
                break;
            case SmsTemplateCodeEnum.考试通过通知:
                smsLog.Type = SmsSendTypeEnum.ExamPass;
                break;
            case SmsTemplateCodeEnum.准考证下发通知:
                smsLog.Type = SmsSendTypeEnum.AdmissionTicketIssued;
                break;
            default:
                throw Oops.Oh("未知的短信模板");
        }
        //发送短信
        AlibabaCloudSms.SendSms(smsLog);
    }

    /// <summary>
    /// 导入准考证PDF文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导入准考证PDF文件")]
    public async Task<ImportAdmissionTicketOutput> ImportAdmissionTicket([FromForm] ImportAdmissionTicketInput input)
    {
        return await _courseCertService.ImportAdmissionTicket(input);
    }

    /// <summary>
    /// 确认导入准考证
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("确认导入准考证")]
    public async Task<bool> ConfirmImportAdmissionTicket(ConfirmImportAdmissionTicketInput input)
    {
        return await _courseCertService.ConfirmImportAdmissionTicket(input);
    }

    /// <summary>
    /// 单独上传准考证文件
    /// </summary>
    /// <param name="input"></param>
    /// <returns>返回OSS文件路径</returns>
    [DisplayName("单独上传准考证文件")]
    public async Task<string> UploadSingleAdmissionTicket([FromForm] UploadSingleAdmissionTicketInput input)
    {
        return await _courseCertService.UploadSingleAdmissionTicket(input);
    }

    #region 身份证识别测试

    /*
    /// <summary>
    /// 身份证识别测试
    /// </summary>
    /// <returns></returns>
    [DisplayName("身份证识别测试")]
    public async Task<string> IDCARDTest()
    {
        string url = "https://hrshequnew.oss-cn-beijing.aliyuncs.com/test/upload/circleContent/2025/06/14/21466932918213.png";

        var info = AlibabaCloudOcr.GetIdCardByPhoto(url);
        return JSON.Serialize(info);
    }
    /// <summary>
    /// 测试两个图片合并pdf
    /// </summary>
    /// <returns></returns>
    [DisplayName("测试两个图片合并pdf")]
    public async Task<SysFile> Test()
    {
        var list = new List<CourseCertUserInfo>();

        list.Add(await _courseCertService.GetDetail(21503899449925));
        //list.Add(await _courseCertService.GetDetail(17617619005253));

        var fileList = new List<DownLoadFileInput>();
        foreach (var item in list)
        {
            var dirName = $"";
            //item.IDCardFrontUrl = "dev/upload/Course/2024/02/28/16247596998853.png";
            //身份证
            if (!string.IsNullOrWhiteSpace(item.IDCardFrontUrl))
            {
                fileList.Add(ConvertDownLoadFile(item.IDCardFrontUrl, $"{dirName}身份证人像"));
            }
            fileList.Add(ConvertDownLoadFile("dev/upload/Course/2024/02/28/16247596998853.png", $"身份证国徽"));
            ////身份证
            //if (!string.IsNullOrWhiteSpace(item.IDCardBackUrl))
            //{
            //    fileList.Add(ConvertDownLoadFile(item.IDCardBackUrl, $"{dirName}身份证国徽"));
            //}
            ////小二寸免冠证件照
            //if (!string.IsNullOrWhiteSpace(item.IdPhotoUrl))
            //{
            //    fileList.Add(ConvertDownLoadFile(item.IdPhotoUrl, $"{dirName}{item.Name}{item.IDCard}"));
            //}
            ////学信网学历电子注册备案表
            //if (!string.IsNullOrWhiteSpace(item.ChsiEleUrl))
            //{
            //    fileList.Add(ConvertDownLoadFile(item.ChsiEleUrl, $"{dirName}教育部学历证书电子注册备案表"));
            //}
            ////毕业证原件视频
            //if (!string.IsNullOrWhiteSpace(item.DiplomaVideoUrl))
            //{
            //    fileList.Add(ConvertDownLoadFile(item.DiplomaVideoUrl, $"{dirName}毕业证视频"));
            //}
            ////毕业证彩色扫描件
            //if (!string.IsNullOrWhiteSpace(item.DiplomaPhotoUrl))
            //{
            //    fileList.Add(ConvertDownLoadFile(item.DiplomaPhotoUrl, $"{dirName}毕业证扫描件"));
            //}
        }
        return await _sysFileService.ImageConvertToPdf(fileList,"身份证");

    }
    */
    #endregion

    /// <summary>
    /// 刷题记录不分页列表
    /// </summary>
    /// <param name="courseId">课程ID</param>
    /// <param name="userId">用户ID</param>
    /// <returns></returns>
    [DisplayName("刷题记录不分页列表")]
    public async Task<List<ExamRecordListOutput>> GetExamRecordList([FromQuery] long courseId, [FromQuery] long userId)
    {
        var input = new ExamRecordListInput
        {
            CourseId = courseId,
            UserId = userId
        };
        return await _courseCertService.GetExamRecordList(input);
    }
}
