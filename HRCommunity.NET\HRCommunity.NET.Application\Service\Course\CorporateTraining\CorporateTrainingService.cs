
using Aop.Api.Domain;
using Nest;
using System.IO.Compression;
using System.Net.Http;
using OnceMi.AspNetCore.OSS;
using HRCommunity.NET.Application.IService;

namespace HRCommunity.NET.Application;

/// <summary>
/// 企业内训
/// </summary>
public class CorporateTrainingService : ICorporateTrainingService
{
    private readonly SqlSugarRepository<OfflineCourseUser> _trainingUserRep;
    private readonly SqlSugarRepository<CorporateTraining> _trainingRep;
    private readonly SqlSugarRepository<OfflineCourse> _offlineCourseRep;
    private readonly SqlSugarRepository<OfflineInteraction> _interactionRep;
    private readonly SqlSugarRepository<CorporateTrainingExam> _trainingExamRep;
    private readonly SqlSugarRepository<CorporateTrainingExamQuestion> _trainingExamQuestionRep;
    private readonly SqlSugarRepository<ExamPaper> _examPaperRep;
    private readonly SqlSugarRepository<ExamPaperQuestion> _examPaperQuestionRep;
    private readonly SqlSugarRepository<ExamQuestions> _examQuestionRep;
    private readonly SqlSugarRepository<ExamQuestionOptions> _examQuestionOptionRep;
    private readonly SqlSugarRepository<CorporateTrainingExamResult> _examResultRep;
    private readonly SqlSugarRepository<CorporateTrainingExamAnswer> _examAnswerRep;
    private readonly SqlSugarRepository<CorporateTrainingExamWrong> _examWrongRep;
    private readonly SqlSugarRepository<CorporateTrainingHomework> _homeworkRep;
    private readonly SqlSugarRepository<CorporateTrainingHomeworkSubmission> _homeworkSubmissionRep;
    private readonly SqlSugarRepository<UserInfo> _userInfoRep;
    private readonly UserManager _userManager;
    private readonly ISysCacheService _sysCacheService;
    private readonly SqlSugarRepository<CorporateTrainingHomeworkLike> _homeworkLikeRep;
    private readonly SqlSugarRepository<CorporateTrainingHomeworkComment> _homeworkCommentRep;
    private readonly SqlSugarRepository<CorporateTrainingHomeworkCommentLike> _homeworkCommentLikeRep;
    private readonly OSSProviderOptions _OSSProviderOptions;
    private readonly IOSSService _OSSService;
    private readonly ISysFileService _sysFileService;
    public CorporateTrainingService(SqlSugarRepository<OfflineCourseUser> trainingUserRep,
                                    SqlSugarRepository<CorporateTraining> trainingRep,
                                    SqlSugarRepository<OfflineCourse> offlineCourseRep,
                                    SqlSugarRepository<OfflineInteraction> interactionRep,
                                    SqlSugarRepository<CorporateTrainingExam> trainingExamRep,
                                    SqlSugarRepository<CorporateTrainingExamQuestion> trainingExamQuestionRep,
                                    SqlSugarRepository<ExamPaper> examPaperRep,
                                    SqlSugarRepository<ExamPaperQuestion> examPaperQuestionRep,
                                    SqlSugarRepository<ExamQuestions> examQuestionRep,
                                    SqlSugarRepository<ExamQuestionOptions> examQuestionOptionRep,
                                    SqlSugarRepository<CorporateTrainingExamResult> examResultRep,
                                    SqlSugarRepository<CorporateTrainingExamAnswer> examAnswerRep,
                                    SqlSugarRepository<CorporateTrainingExamWrong> examWrongRep,
                                    SqlSugarRepository<CorporateTrainingHomework> homeworkRep,
                                    SqlSugarRepository<CorporateTrainingHomeworkSubmission> homeworkSubmissionRep,
                                    SqlSugarRepository<UserInfo> userInfoRep,
                                    UserManager userManager,
                                    ISysCacheService sysCacheService,
                                    SqlSugarRepository<CorporateTrainingHomeworkLike> homeworkLikeRep,
                                    SqlSugarRepository<CorporateTrainingHomeworkComment> homeworkCommentRep,
                                    SqlSugarRepository<CorporateTrainingHomeworkCommentLike> homeworkCommentLikeRep,
                                    IOptions<OSSProviderOptions> oSSProviderOptions,
                                    IOSSServiceFactory ossServiceFactory,
                                    ISysFileService sysFileService)
    {
        _trainingUserRep = trainingUserRep;
        _trainingRep = trainingRep;
        _offlineCourseRep = offlineCourseRep;
        _interactionRep = interactionRep;
        _trainingExamRep = trainingExamRep;
        _trainingExamQuestionRep = trainingExamQuestionRep;
        _examPaperRep = examPaperRep;
        _examPaperQuestionRep = examPaperQuestionRep;
        _examQuestionRep = examQuestionRep;
        _examQuestionOptionRep = examQuestionOptionRep;
        _examResultRep = examResultRep;
        _examAnswerRep = examAnswerRep;
        _examWrongRep = examWrongRep;
        _homeworkRep = homeworkRep;
        _homeworkSubmissionRep = homeworkSubmissionRep;
        _userInfoRep = userInfoRep;
        _userManager = userManager;
        _sysCacheService = sysCacheService;
        _homeworkLikeRep = homeworkLikeRep;
        _homeworkCommentRep = homeworkCommentRep;
        _homeworkCommentLikeRep = homeworkCommentLikeRep;
        _OSSProviderOptions = oSSProviderOptions.Value;
        if (_OSSProviderOptions.IsEnable)
            _OSSService = ossServiceFactory.Create(Enum.GetName(_OSSProviderOptions.Provider));
        _sysFileService = sysFileService;
    }
    #region 后端方法

    /// <summary>
    /// 获取企业内训分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<PageCorporateTrainingOutput>> Page(PageCorporateTrainingInput input)
    {
        //查询当前登录人
        var isAdmin = false;
        if(_userManager.AccountType == AccountTypeEnum.SuperAdmin || _userManager.AccountType == AccountTypeEnum.Admin)
        {
            isAdmin = true;
        }
        //else if (_userManager.AccountType != AccountTypeEnum.SuperAdmin)
        //{
        //    var admin = await _trainingRep.ChangeRepository<SqlSugarRepository<SysAdmin>>().AsQueryable().Includes(x=>x.Role).Where(x=>x.Id == _userManager.UserId).FirstAsync();

        //    isAdmin = (admin != null && admin.Role.Code == "sys_admin") ? true : false;
        //}

        var mainList = await _trainingRep.AsQueryable()
            .InnerJoin<SysAdmin>((x,u) => x.CreateUserId == u.Id)
            .LeftJoin<OfflineCourse>((x,u,o) => x.Id == o.TrainingId)
            .WhereIF(!isAdmin, x=>x.CreateUserId == _userManager.UserId)//只有系统管理员可以查看所有数据，其他角色只能查看自己创建的项目
            .WhereIF(!string.IsNullOrWhiteSpace(input.EnterpriseName), x => x.EnterpriseName.Contains(input.EnterpriseName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ProjectName), x => x.ProjectName.Contains(input.ProjectName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ProjectManager), x => x.ProjectManager.Contains(input.ProjectManager))
            .WhereIF(input.RealStatus.HasValue && input.RealStatus == 3, x => x.Status == StatusEnum.Disable)
            .WhereIF(input.RealStatus.HasValue && input.RealStatus == 0, x => x.Status == StatusEnum.Enable && x.StartTime > DateTime.Now)
            .WhereIF(input.RealStatus.HasValue && input.RealStatus == 2, x => x.Status == StatusEnum.Enable && x.EndTime <= DateTime.Now)
            .WhereIF(input.RealStatus.HasValue && input.RealStatus == 1, x => x.Status == StatusEnum.Enable && x.StartTime <= DateTime.Now && DateTime.Now< x.EndTime)
            .WhereIF(input.StartTime.HasValue && !input.EndTime.HasValue, x => SqlFunc.ToDate(x.StartTime) <= input.StartTime && input.StartTime <= SqlFunc.ToDate(x.EndTime)) //只有起始时间筛选
            .WhereIF(!input.StartTime.HasValue && input.EndTime.HasValue, x => SqlFunc.ToDate(x.StartTime) <= input.EndTime && input.EndTime <= SqlFunc.ToDate(x.EndTime)) //只有结束时间筛选
            .WhereIF(input.StartTime.HasValue && input.EndTime.HasValue, x => (input.StartTime <= x.StartTime && x.StartTime < input.EndTime.Value.AddDays(1)) || (input.StartTime <= x.EndTime && x.EndTime < input.EndTime.Value.AddDays(1)) || (SqlFunc.ToDate(x.StartTime) <= input.StartTime && input.EndTime <= SqlFunc.ToDate(x.EndTime))) //起始时间和结束时间同时存在筛选
            .OrderByDescending(x => x.CreateTime)
            .Select((x, u, o) => new PageCorporateTrainingOutput
            {
                UserName = u.RealName,
                PNumber = SqlFunc.Subqueryable<OfflineCourseUser>().Where(u => u.OfflineCourseId == o.Id).Count()
            }, true)
            .ToPagedListAsync(input.Page, input.PageSize);

        return mainList;
    }

    /// <summary>
    /// 获取企业内训详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<CorporateTraining> GetDetail(long id)
    {
        return await _trainingRep.GetByIdAsync(id) ?? throw Oops.Oh("企业内训不存在");
    } 
    /// <summary>
    /// 新增/编辑企业内训
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<(bool,long)> Save(AddCorporateTrainingInput input)
    {
        #region
        if(string.IsNullOrWhiteSpace(input.ProjectNo))
            throw Oops.Oh("请输入项目编号");
        if(string.IsNullOrWhiteSpace(input.EnterpriseName))
            throw Oops.Oh("请输入企业名称");
        if(string.IsNullOrWhiteSpace(input.ProjectName))
            throw Oops.Oh("请输入项目名称");
        if(string.IsNullOrWhiteSpace(input.InstructorName))
            throw Oops.Oh("请输入讲师姓名");
        if(string.IsNullOrWhiteSpace(input.ProjectManager))
            throw Oops.Oh("请输入项目经理");
        if(input.CategoryId == 0 || input.ParentCategoryId == 0)
            throw Oops.Oh("请选择课程分类");
        if(input.StartTime > input.EndTime)
            throw Oops.Oh("开始时间不能大于结束时间");
        if(string.IsNullOrWhiteSpace(input.Location))
            throw Oops.Oh("请输入执行地点");
        if(input.IsDesign != YesNoEnum.Y && input.IsDesign != YesNoEnum.N)
            throw Oops.Oh("请设置是否指定学员");
        #endregion
        if (input.Id > 0)
        {
            var training = await _trainingRep.GetByIdAsync(input.Id) ?? throw Oops.Oh("企业内训不存在");
            var rows = await _trainingRep.AsUpdateable(input.Adapt<CorporateTraining>())
                        .ExecuteCommandAsync();
            return (rows > 0,input.Id);
        }
        else
        {
            var newDto = await _trainingRep.InsertReturnEntityAsync(input.Adapt<CorporateTraining>());
            return (true, newDto.Id);
        }
    }

    /// <summary>
    /// 更新企业内训状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> SetStatus(CorporateTrainingStatusInput input)
    {
        var dto = await _trainingRep.GetByIdAsync(input.Id) ?? throw Oops.Oh("企业内训不存在");
        dto.Status = input.Status;
        dto.UpdateTime = DateTime.Now;
        return await _trainingRep.UpdateAsync(dto);
    }

    /// <summary>
    /// 获取试卷题目列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<ExamPaperQuestionOutput>> GetExamPaperQuestions(GetExamPaperQuestionsInput input)
    {
        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 获取试卷下的所有题目
        var questions = await _examPaperQuestionRep.AsQueryable()
            .LeftJoin<ExamQuestions>((pq, q) => pq.ExamQuestionId == q.Id)
            .LeftJoin<ExamQuestionOptions>((pq, q, opt) => q.Id == opt.ExamQuestionId)
            .Where((pq, q, opt) => pq.ExamPaperId == input.ExamPaperId && q.Status == StatusEnum.Enable)
            .OrderBy((pq, q, opt) => q.CreateTime)
            .Select((pq, q, opt) => new
            {
                QuestionId = q.Id,
                QuestionType = q.QuestionType,
                Title = q.Title,
                TitleImage = q.TitleImage,
                OptionId = opt.Id,
                OptionContent = opt.Content,
                OptionContentImage = opt.ContentImage,
                IsTrue = opt.IsTrue
            })
            .ToListAsync();

        // 按题目分组并构建输出
        var result = questions.GroupBy(x => x.QuestionId)
            .Select((group, index) =>
            {
                var firstQuestion = group.First();
                var options = group.Where(x => x.OptionId > 0)
                    .Select((opt, optIndex) => new ExamQuestionOptionOutput
                    {
                        Id = opt.OptionId,
                        Content = opt.OptionContent,
                        ContentImage = opt.OptionContentImage,
                        OptionLabel = ((char)('A' + optIndex)).ToString(),
                        IsTrue = opt.IsTrue
                    }).ToList();

                return new ExamPaperQuestionOutput
                {
                    Id = firstQuestion.QuestionId,
                    QuestionType = firstQuestion.QuestionType,
                    Title = firstQuestion.Title,
                    TitleImage = firstQuestion.TitleImage,
                    Options = options
                };
            }).ToList();

        return result;
    }

    /// <summary>
    /// 配置线下课考试（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> ConfigExam(ConfigCorporateTrainingExamInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 验证题目是否都属于该试卷
        var paperQuestionIds = await _examPaperQuestionRep.AsQueryable()
            .Where(pq => pq.ExamPaperId == input.ExamPaperId)
            .Select(pq => pq.ExamQuestionId)
            .ToListAsync();

        var inputQuestionIds = input.QuestionScores.Select(x => x.ExamQuestionId).ToList();
        var invalidQuestionIds = inputQuestionIds.Except(paperQuestionIds).ToList();
        if (invalidQuestionIds.Any())
            throw Oops.Oh($"题目ID {string.Join(",", invalidQuestionIds)} 不属于该试卷");

        long interactionId;

        if (input.OfflineInteractionId == 0)
        {
            // 新增互动
            var newInteraction = await _interactionRep.InsertReturnEntityAsync(new OfflineInteraction()
            {
                OfflineCourseId = input.OfflineCourseId,
                Type = 2, // 考试类型
                Name = input.ExamName,
                QNumber = paperQuestionIds.Count,
                ExamPaperId = input.ExamPaperId,
            });
            interactionId = newInteraction.Id;
        }
        else
        {
            // 验证互动是否存在且为考试类型
            var interaction = await _interactionRep.AsQueryable()
                .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2);
            if (interaction == null)
                throw Oops.Oh("考试互动不存在");

            // 更新互动信息
            await _interactionRep.UpdateAsync(x => new OfflineInteraction()
            {
                Name = input.ExamName,
                QNumber = paperQuestionIds.Count,
                UpdateTime = DateTime.Now,
                ExamPaperId = input.ExamPaperId,
            }, x => x.Id == input.OfflineInteractionId);

            interactionId = input.OfflineInteractionId;
        }

        // 检查是否已存在配置，如果存在则更新，否则新增
        var existingExam = await _trainingExamRep.AsQueryable()
            .Where(te => te.OfflineCourseId == input.OfflineCourseId &&
                        te.OfflineInteractionId == interactionId &&
                        te.ExamPaperId == input.ExamPaperId)
            .FirstAsync();

        long examConfigId;
        if (existingExam != null)
        {
            // 更新考试配置
            existingExam.ExamName = input.ExamName;
            existingExam.ExamPaperId = input.ExamPaperId;
            await _trainingExamRep.UpdateAsync(existingExam);
            examConfigId = existingExam.Id;

            // 删除原有的题目分数配置
            await _trainingExamQuestionRep.DeleteAsync(teq => teq.CorporateTrainingExamId == examConfigId);
        }
        else
        {
            // 新增考试配置
            var newExam = new CorporateTrainingExam
            {
                OfflineCourseId = input.OfflineCourseId,
                OfflineInteractionId = interactionId,
                ExamPaperId = input.ExamPaperId,
                ExamName = input.ExamName
            };
            var insertedExam = await _trainingExamRep.AsInsertable(newExam).ExecuteReturnEntityAsync();
            examConfigId = insertedExam.Id;
        }

        // 插入新的题目分数配置
        var examQuestions = input.QuestionScores.Select(qs => new CorporateTrainingExamQuestion
        {
            CorporateTrainingExamId = examConfigId,
            ExamQuestionId = qs.ExamQuestionId,
            QuestionScore = qs.QuestionScore
        }).ToList();

        await _trainingExamQuestionRep.InsertRangeAsync(examQuestions);

        return true;
    }

    /// <summary>
    /// 获取线下课考试配置（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CorporateTrainingExamOutput> GetExamConfig(GetCorporateTrainingExamInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 如果互动ID为0，表示还没有创建互动，直接返回null
        if (input.OfflineInteractionId == 0)
            return null;

        // 验证互动是否存在且为考试类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2);
        if (interaction == null)
            throw Oops.Oh("考试互动不存在");

        // 获取考试配置
        var examConfig = await _trainingExamRep.AsQueryable()
            .LeftJoin<ExamPaper>((te, ep) => te.ExamPaperId == ep.Id)
            .Where((te, ep) => te.OfflineCourseId == input.OfflineCourseId && te.OfflineInteractionId == input.OfflineInteractionId)
            .Select((te, ep) => new CorporateTrainingExamOutput
            {
                Id = te.Id,
                ExamPaperId = te.ExamPaperId,
                ExamPaperName = ep.Name,
                ExamName = te.ExamName,
                CreateTime = te.CreateTime.Value
            })
            .FirstAsync();

        if (examConfig == null)
            return null;

        // 获取题目分数配置
        var questionScores = await _trainingExamQuestionRep.AsQueryable()
            .LeftJoin<ExamQuestions>((teq, q) => teq.ExamQuestionId == q.Id)
            .Where((teq, q) => teq.CorporateTrainingExamId == examConfig.Id)
            .Select((teq, q) => new ExamQuestionScoreOutput
            {
                ExamQuestionId = teq.ExamQuestionId,
                QuestionTitle = q.Title,
                TitleImage = q.TitleImage,
                QuestionType = q.QuestionType,
                QuestionScore = teq.QuestionScore
            })
            .ToListAsync();

        examConfig.QuestionScores = questionScores;
        examConfig.QuestionCount = questionScores.Count;
        examConfig.TotalScore = questionScores.Sum(x => x.QuestionScore);

        return examConfig;
    }

    /// <summary>
    /// 获取线下课考试题目列表（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<CorporateTrainingExamQuestionsOutput>> GetExamQuestions(CorporateTrainingExamQuestionsInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证互动是否存在且为考试类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2 && x.Status == StatusEnum.Enable);
        if (interaction == null)
            throw Oops.Oh("考试互动不存在");

        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 验证是否已配置该线下课的考试
        var examConfig = await _trainingExamRep.AsQueryable()
            .Where(te => te.OfflineCourseId == input.OfflineCourseId &&
                        te.OfflineInteractionId == input.OfflineInteractionId &&
                        te.ExamPaperId == input.ExamPaperId)
            .FirstAsync();

        if (examConfig == null)
            throw Oops.Oh("该线下课尚未配置此试卷的考试");

        // 获取试卷下的题目列表，参考H5List的实现逻辑
        var ret = await _examQuestionRep.AsQueryable()
           .Includes(u => u.Options)
           .Where(x => x.Status == StatusEnum.Enable)
           .Where(u => SqlFunc.Subqueryable<ExamPaperQuestion>()
               .Where(pq => pq.ExamPaperId == input.ExamPaperId && pq.ExamQuestionId == u.Id)
               .Any())
           .OrderBy(u => u.CreateTime)
           .Select(u => new CorporateTrainingExamQuestionsOutput
           {
               Id = u.Id,
               Title = u.Title,
               TitleImage = u.TitleImage,
               QuestionType = u.QuestionType,
               OptionsAnswer = u.Options,
               Analysis = u.Analysis
           }).ToListAsync();

        // 将题目和答案存入缓存，用于后续交卷时对答案
        var cacheKey = $"{CacheConst.KeyUserExam}{_userManager.UserId}:{input.ExamPaperId}:{input.OfflineCourseId}:{input.OfflineInteractionId}";
        var cacheQuestions = ret.Select(u => new H5ExamQuestionsListWithAnswer
        {
            Id = u.Id,
            Title = u.Title,
            TitleImage = u.TitleImage,
            QuestionType = u.QuestionType,
            Options = u.OptionsAnswer?.Select(opt => new WrongOptions
            {
                Id = opt.Id,
                Options = opt.Options,
                Content = opt.Content,
                ContentImage = opt.ContentImage,
                IsTrue = opt.IsTrue
            }).ToList() ?? new List<WrongOptions>(),
            Analysis = u.Analysis,
            Status = 0 // 未答题状态
        }).ToList();

        _sysCacheService.Set(cacheKey, cacheQuestions, TimeSpan.FromHours(2));

        return ret;
    }

    /// <summary>
    /// 提交线下课考试答案（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<CorporateTrainingExamSubmitOutput> SubmitExam(CorporateTrainingExamSubmitInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证互动是否存在且为考试类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2 && x.Status == StatusEnum.Enable);
        if (interaction == null)
            throw Oops.Oh("考试互动不存在");

        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 验证是否已配置该线下课的考试
        var examConfig = await _trainingExamRep.AsQueryable()
            .Where(te => te.OfflineCourseId == input.OfflineCourseId &&
                        te.OfflineInteractionId == input.OfflineInteractionId &&
                        te.ExamPaperId == input.ExamPaperId)
            .FirstAsync();

        if (examConfig == null)
            throw Oops.Oh("该线下课尚未配置此试卷的考试");

        // 检查用户是否已经提交过答案
        var existingResult = await _examResultRep.AsQueryable()
            .Where(er => er.UserId == _userManager.UserId
                      && er.OfflineCourseId == input.OfflineCourseId
                      && er.OfflineInteractionId == input.OfflineInteractionId
                      && er.ExamPaperId == input.ExamPaperId)
            .FirstAsync();

        if (existingResult != null)
            throw Oops.Oh("您已经提交过该考试的答案，不能重复提交");

        // 从缓存获取题目和答案信息
        var cacheKey = $"{CacheConst.KeyUserExam}{_userManager.UserId}:{input.ExamPaperId}:{input.OfflineCourseId}:{input.OfflineInteractionId}";
        var cacheQuestions = _sysCacheService.Get<List<H5ExamQuestionsListWithAnswer>>(cacheKey);

        if (cacheQuestions == null || !cacheQuestions.Any())
            throw Oops.Oh("考试数据已过期，请重新获取题目");

        // 获取题目分数配置
        var questionScoreList = await _trainingExamQuestionRep.AsQueryable()
            .Where(teq => teq.CorporateTrainingExamId == examConfig.Id)
            .Select(teq => new { QuestionId = teq.ExamQuestionId, Score = teq.QuestionScore })
            .ToListAsync();
        var questionScores = questionScoreList.ToDictionary(x => x.QuestionId, x => x.Score);

        var submitTime = DateTime.Now;
        var durationSeconds = (int)(submitTime - input.StartTime).TotalSeconds;

        int correctAnswers = 0;
        decimal totalScore = 0;
        var examAnswers = new List<CorporateTrainingExamAnswer>();
        var wrongQuestions = new List<CorporateTrainingExamWrong>();

        // 处理每道题的答案
        foreach (var answer in input.Answers)
        {
            var question = cacheQuestions.FirstOrDefault(q => q.Id == answer.QuestionId);
            if (question == null) continue;

            var questionScore = questionScores.TryGetValue(answer.QuestionId, out var score) ? score : 0m;

            // 判断答案是否正确
            var correctOptionIds = question.Options.Where(o => o.IsTrue).Select(o => o.Id).OrderBy(x => x).ToList();
            var userOptionIds = answer.OptionIds.OrderBy(x => x).ToList();
            var isCorrect = correctOptionIds.SequenceEqual(userOptionIds);

            if (isCorrect)
            {
                correctAnswers++;
                totalScore += questionScore;
            }
            else
            {
                // 添加到错题记录
                var wrongOptions = question.Options.Select(opt => new CorporateTrainingWrongOptionOutput
                {
                    Id = opt.Id,
                    Options = opt.Options,
                    Content = opt.Content,
                    ContentImage = opt.ContentImage,
                    IsTrue = opt.IsTrue,
                    IsUserChoice = answer.OptionIds.Contains(opt.Id)
                }).ToList();

                wrongQuestions.Add(new CorporateTrainingExamWrong
                {
                    UserId = _userManager.UserId,
                    OfflineCourseId = input.OfflineCourseId,
                    OfflineInteractionId = input.OfflineInteractionId,
                    ExamPaperId = input.ExamPaperId,
                    QuestionId = answer.QuestionId,
                    QuestionType = question.QuestionType,
                    Title = question.Title,
                    Analysis = question.Analysis,
                    Options = JSON.Serialize(wrongOptions)
                });
            }

            // 添加答题详情
            examAnswers.Add(new CorporateTrainingExamAnswer
            {
                ExamQuestionId = answer.QuestionId,
                UserAnswerOptionIds = string.Join(",", answer.OptionIds),
                IsCorrect = isCorrect,
                QuestionScore = questionScore,
                EarnedScore = isCorrect ? questionScore : 0m
            });
        }



        // 保存考试结果
        var examResult = new CorporateTrainingExamResult
        {
            UserId = _userManager.UserId,
            OfflineCourseId = input.OfflineCourseId,
            OfflineInteractionId = input.OfflineInteractionId,
            ExamPaperId = input.ExamPaperId,
            CorporateTrainingExamId = examConfig.Id,
            TotalQuestions = cacheQuestions.Count,
            CorrectAnswers = correctAnswers,
            TotalScore = totalScore,
            StartTime = input.StartTime,
            SubmitTime = submitTime,
            DurationSeconds = durationSeconds
        };

        var insertedResult = await _examResultRep.AsInsertable(examResult).ExecuteReturnEntityAsync();

        // 设置答题详情的考试结果ID并保存
        foreach (var examAnswer in examAnswers)
        {
            examAnswer.CorporateTrainingExamResultId = insertedResult.Id;
        }
        await _examAnswerRep.InsertRangeAsync(examAnswers);

        // 保存错题记录
        if (wrongQuestions.Any())
        {
            await _examWrongRep.InsertRangeAsync(wrongQuestions);
        }

        // 清除缓存
        _sysCacheService.Remove(cacheKey);

        return new CorporateTrainingExamSubmitOutput
        {
            ExamResultId = insertedResult.Id,
            ExamName = examConfig.ExamName,
            TotalQuestions = examResult.TotalQuestions,
            CorrectAnswers = examResult.CorrectAnswers,
            TotalScore = examResult.TotalScore,
            DurationSeconds = examResult.DurationSeconds,
            SubmitTime = examResult.SubmitTime
        };
    }

    /// <summary>
    /// 获取线下课考试结果（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CorporateTrainingExamResultOutput> GetExamResult(CorporateTrainingExamResultInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证互动是否存在且为考试类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2 && x.Status == StatusEnum.Enable);
        if (interaction == null)
            throw Oops.Oh("考试互动不存在");

        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 获取用户的考试结果
        var examResult = await _examResultRep.AsQueryable()
            .LeftJoin<CorporateTrainingExam>((er, te) => er.CorporateTrainingExamId == te.Id)
            .LeftJoin<ExamPaper>((er, te, ep) => er.ExamPaperId == ep.Id)
            .Where((er, te, ep) => er.UserId == _userManager.UserId
                                && er.OfflineCourseId == input.OfflineCourseId
                                && er.OfflineInteractionId == input.OfflineInteractionId
                                && er.ExamPaperId == input.ExamPaperId)
            .Select((er, te, ep) => new CorporateTrainingExamResultOutput
            {
                ExamResultId = er.Id,
                ExamName = te.ExamName,
                ExamPaperName = ep.Name,
                TotalQuestions = er.TotalQuestions,
                CorrectAnswers = er.CorrectAnswers,
                TotalScore = er.TotalScore,
                DurationSeconds = er.DurationSeconds,
                StartTime = er.StartTime,
                SubmitTime = er.SubmitTime
            })
            .FirstAsync();

        if (examResult == null)
            throw Oops.Oh("未找到考试结果");

        // 获取错题列表
        var wrongQuestions = await _examWrongRep.AsQueryable()
            .Where(ew => ew.UserId == _userManager.UserId
                      && ew.OfflineCourseId == input.OfflineCourseId
                      && ew.OfflineInteractionId == input.OfflineInteractionId
                      && ew.ExamPaperId == input.ExamPaperId)
            .Select(ew => new CorporateTrainingExamWrongOutput
            {
                Id = ew.Id,
                QuestionId = ew.QuestionId,
                QuestionType = ew.QuestionType,
                Title = ew.Title,
                Analysis = ew.Analysis,
                OptionsJson = ew.Options
            })
            .ToListAsync();

        examResult.WrongQuestions = wrongQuestions;

        return examResult;
    }

    /// <summary>
    /// 获取线下课考试排行榜（分页版本，支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CorporateTrainingExamRankingPageOutput> GetExamRankingPage(CorporateTrainingExamRankingPageInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证互动是否存在且为考试类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2 && x.Status == StatusEnum.Enable);
        if (interaction == null)
            throw Oops.Oh("考试互动不存在");

        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 构建基础查询
        var baseQuery = _examResultRep.AsQueryable()
            .LeftJoin<UserInfo>((er, ui) => er.UserId == ui.Id)
            .Where((er, ui) => er.OfflineCourseId == input.OfflineCourseId
                            && er.OfflineInteractionId == input.OfflineInteractionId
                            && er.ExamPaperId == input.ExamPaperId)
            .OrderByDescending((er, ui) => er.TotalScore)
            .OrderBy((er, ui) => er.DurationSeconds)
            .OrderBy((er, ui) => er.SubmitTime);

        // 获取分页数据
        var pagedRankings = await baseQuery
            .Select((er, ui) => new CorporateTrainingExamRankingOutput
            {
                UserId = er.UserId ?? 0,
                Avatar = ui.Avatar,
                UserName = ui.RealName ?? ui.NickName,
                TotalScore = er.TotalScore,
                DurationSeconds = er.DurationSeconds,
                CorrectAnswers = er.CorrectAnswers,
                TotalQuestions = er.TotalQuestions,
                SubmitTime = er.SubmitTime
            })
            .ToPagedListAsync(input.Page, input.PageSize);

        // 设置分页数据的排名（基于当前页的起始排名）
        var startRank = (input.Page - 1) * input.PageSize + 1;
        for (int i = 0; i < pagedRankings.Items.Count(); i++)
        {
            ((List<CorporateTrainingExamRankingOutput>)pagedRankings.Items)[i].Rank = startRank + i;
        }

        // 查询当前用户的排名
        CorporateTrainingExamRankingOutput? myRanking = null;
        var currentUserId = _userManager.UserId;

        // 先查询当前用户是否有考试记录
        var currentUserResult = await baseQuery
            .Where((er, ui) => er.UserId == currentUserId)
            .Select((er, ui) => new CorporateTrainingExamRankingOutput
            {
                UserId = er.UserId ?? 0,
                Avatar = ui.Avatar,
                UserName = ui.RealName ?? ui.NickName,
                TotalScore = er.TotalScore,
                DurationSeconds = er.DurationSeconds,
                CorrectAnswers = er.CorrectAnswers,
                TotalQuestions = er.TotalQuestions,
                SubmitTime = er.SubmitTime
            })
            .FirstAsync();

        if (currentUserResult != null)
        {
            // 计算当前用户的排名：比当前用户分数高的人数 + 1
            var betterCount = await _examResultRep.AsQueryable()
                .Where(er => er.OfflineCourseId == input.OfflineCourseId
                          && er.OfflineInteractionId == input.OfflineInteractionId
                          && er.ExamPaperId == input.ExamPaperId
                          && (er.TotalScore > currentUserResult.TotalScore
                              || (er.TotalScore == currentUserResult.TotalScore && er.DurationSeconds < currentUserResult.DurationSeconds)
                              || (er.TotalScore == currentUserResult.TotalScore && er.DurationSeconds == currentUserResult.DurationSeconds && er.SubmitTime < currentUserResult.SubmitTime)))
                .CountAsync();

            currentUserResult.Rank = betterCount + 1;
            myRanking = currentUserResult;
        }

        return new CorporateTrainingExamRankingPageOutput
        {
            PageData = pagedRankings,
            MyRanking = myRanking
        };
    }

    /// <summary>
    /// 获取线下课考试基本信息分页（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<CorporateTrainingExamBasicInfoOutput>> GetExamBasicInfoPage(CorporateTrainingExamBasicInfoPageInput input)
    {
        // 验证线下课考试配置是否存在
        var examConfig = await _trainingExamRep.AsQueryable().FirstAsync(v=>v.OfflineInteractionId == input.CorporateTrainingExamId);
        if (examConfig == null)
            throw Oops.Oh("线下课考试配置不存在");
        //long CorporateTrainingExamIdNew = examConfig.Id;
        input.CorporateTrainingExamId = examConfig.Id;

        // 获取提交的答题人数
        var submittedCount = await _examResultRep.AsQueryable()
            .Where(er => er.CorporateTrainingExamId == input.CorporateTrainingExamId)
            .CountAsync();

        // 获取平均得分和平均正确率
        var examResults = await _examResultRep.AsQueryable()
            .Where(er => er.CorporateTrainingExamId == input.CorporateTrainingExamId)
            .Select(er => new { er.TotalScore, er.CorrectAnswers, er.TotalQuestions })
            .ToListAsync();

        decimal averageScore = 0;
        decimal averageAccuracy = 0;
        if (examResults.Any())
        {
            averageScore = examResults.Average(er => er.TotalScore);
            averageAccuracy = examResults.Average(er => er.TotalQuestions > 0 ? (decimal)er.CorrectAnswers / er.TotalQuestions * 100 : 0);
        }

        // 获取题目统计信息
        var questionStats = await _trainingExamQuestionRep.AsQueryable()
            .LeftJoin<ExamQuestions>((teq, q) => teq.ExamQuestionId == q.Id)
            .Where((teq, q) => teq.CorporateTrainingExamId == input.CorporateTrainingExamId)
            .Select((teq, q) => new
            {
                QuestionId = teq.ExamQuestionId,
                QuestionType = q.QuestionType,
                Title = q.Title,
                Score = teq.QuestionScore
            })
            .ToListAsync();

        var questionStatOutputs = new List<CorporateTrainingExamQuestionStatOutput>();
        foreach (var questionStat in questionStats)
        {
            // 获取该题目的答题人数和答对人数
            var answerCount = await _examAnswerRep.AsQueryable()
                .Where(ea => ea.ExamQuestionId == questionStat.QuestionId)
                .Where(ea => SqlFunc.Subqueryable<CorporateTrainingExamResult>()
                    .Where(er => er.Id == ea.CorporateTrainingExamResultId && er.CorporateTrainingExamId == input.CorporateTrainingExamId)
                    .Any())
                .CountAsync();

            var correctCount = await _examAnswerRep.AsQueryable()
                .Where(ea => ea.ExamQuestionId == questionStat.QuestionId && ea.IsCorrect)
                .Where(ea => SqlFunc.Subqueryable<CorporateTrainingExamResult>()
                    .Where(er => er.Id == ea.CorporateTrainingExamResultId && er.CorporateTrainingExamId == input.CorporateTrainingExamId)
                    .Any())
                .CountAsync();

            var accuracy = answerCount > 0 ? (decimal)correctCount / answerCount * 100 : 0;

            questionStatOutputs.Add(new CorporateTrainingExamQuestionStatOutput
            {
                QuestionId = questionStat.QuestionId,
                QuestionType = questionStat.QuestionType,
                Title = questionStat.Title,
                Score = questionStat.Score,
                AnswerCount = answerCount,
                CorrectCount = correctCount,
                Accuracy = accuracy
            });
        }

        var basicInfo = new CorporateTrainingExamBasicInfoOutput
        {
            SubmittedCount = submittedCount,
            AverageScore = averageScore,
            AverageAccuracy = averageAccuracy,
            QuestionStats = questionStatOutputs
        };

        // 创建分页结果（这里只有一条记录）
        var result = new List<CorporateTrainingExamBasicInfoOutput> { basicInfo };
        return result.ToPagedList(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取线下课考试学员信息分页（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<CorporateTrainingExamStudentOutput>> GetExamStudentPage(CorporateTrainingExamStudentPageInput input)
    {
        // 验证线下课考试配置是否存在
        var examConfig = await _trainingExamRep.AsQueryable().FirstAsync(v => v.OfflineInteractionId == input.CorporateTrainingExamId);
        if (examConfig == null)
            throw Oops.Oh("线下课考试配置不存在");
        //long CorporateTrainingExamIdNew = examConfig.Id;
        input.CorporateTrainingExamId = examConfig.Id;

        // 构建查询
        var query = _examResultRep.AsQueryable()
            .LeftJoin<UserInfo>((er, u) => er.UserId == u.Id)
            .Where((er, u) => er.CorporateTrainingExamId == input.CorporateTrainingExamId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), (er, u) => u.RealName.Contains(input.RealName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), (er, u) => u.Phone.Contains(input.Phone));

        // 排序
        if (!string.IsNullOrWhiteSpace(input.SortField))
        {
            var isDesc = !string.IsNullOrWhiteSpace(input.SortDirection) && input.SortDirection.ToLower() == "desc";

            switch (input.SortField.ToLower())
            {
                case "score":
                    query = isDesc ? query.OrderByDescending((er, u) => er.TotalScore) : query.OrderBy((er, u) => er.TotalScore);
                    break;
                case "submittime":
                    query = isDesc ? query.OrderByDescending((er, u) => er.SubmitTime) : query.OrderBy((er, u) => er.SubmitTime);
                    break;
                case "duration":
                    query = isDesc ? query.OrderByDescending((er, u) => er.DurationSeconds) : query.OrderBy((er, u) => er.DurationSeconds);
                    break;
                default:
                    query = query.OrderByDescending((er, u) => er.SubmitTime);
                    break;
            }
        }
        else
        {
            query = query.OrderByDescending((er, u) => er.SubmitTime);
        }

        // 分页查询 - 修复准确率计算问题
        var result = await query.Select((er, u) => new CorporateTrainingExamStudentOutput
        {
            UserId = er.UserId.Value,
            RealName = u.RealName ?? u.NickName,
            Phone = u.Phone,
            TotalScore = er.TotalScore,
            Accuracy = 0, // 先设为0，后面重新计算
            DurationSeconds = er.DurationSeconds,
            SubmitTime = er.SubmitTime
        }).ToPagedListAsync(input.Page, input.PageSize);

        // 修复准确率计算 - 如果TotalQuestions为0，尝试从答题详情重新计算
        foreach (var item in result.Items)
        {
            var examResultData = await _examResultRep.AsQueryable()
                .Where(er => er.UserId == item.UserId && er.CorporateTrainingExamId == input.CorporateTrainingExamId)
                .Select(er => new { er.Id, er.TotalQuestions, er.CorrectAnswers })
                .FirstAsync();

            if (examResultData != null)
            {
                if (examResultData.TotalQuestions > 0)
                {
                    // 如果TotalQuestions正常，直接计算
                    item.Accuracy = (decimal)examResultData.CorrectAnswers / examResultData.TotalQuestions * 100;
                }
                else
                {
                    // 如果TotalQuestions为0，从答题详情重新计算
                    var answerDetails = await _examAnswerRep.AsQueryable()
                        .Where(ea => ea.CorporateTrainingExamResultId == examResultData.Id)
                        .Select(ea => new { ea.IsCorrect })
                        .ToListAsync();

                    if (answerDetails.Any())
                    {
                        var totalQuestions = answerDetails.Count;
                        var correctAnswers = answerDetails.Count(ea => ea.IsCorrect);
                        item.Accuracy = totalQuestions > 0 ? (decimal)correctAnswers / totalQuestions * 100 : 0;


                    }
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 获取线下课考试学员信息导出列表（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<ExportCorporateTrainingExamStudentDto>> GetExamStudentExportList(CorporateTrainingExamStudentPageInput input)
    {
        // 验证线下课考试配置是否存在
        var examConfig = await _trainingExamRep.AsQueryable().FirstAsync(v => v.OfflineInteractionId == input.CorporateTrainingExamId);
        if (examConfig == null)
            throw Oops.Oh("线下课考试配置不存在");
        //long CorporateTrainingExamIdNew = examConfig.Id;
        input.CorporateTrainingExamId = examConfig.Id;

        // 构建查询（不分页）
        var query = _examResultRep.AsQueryable()
            .LeftJoin<UserInfo>((er, u) => er.UserId == u.Id)
            .Where((er, u) => er.CorporateTrainingExamId == input.CorporateTrainingExamId)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), (er, u) => u.RealName.Contains(input.RealName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), (er, u) => u.Phone.Contains(input.Phone));

        // 排序
        if (!string.IsNullOrWhiteSpace(input.SortField))
        {
            var isDesc = !string.IsNullOrWhiteSpace(input.SortDirection) && input.SortDirection.ToLower() == "desc";

            switch (input.SortField.ToLower())
            {
                case "score":
                    query = isDesc ? query.OrderByDescending((er, u) => er.TotalScore) : query.OrderBy((er, u) => er.TotalScore);
                    break;
                case "submittime":
                    query = isDesc ? query.OrderByDescending((er, u) => er.SubmitTime) : query.OrderBy((er, u) => er.SubmitTime);
                    break;
                case "duration":
                    query = isDesc ? query.OrderByDescending((er, u) => er.DurationSeconds) : query.OrderBy((er, u) => er.DurationSeconds);
                    break;
                default:
                    query = query.OrderByDescending((er, u) => er.SubmitTime);
                    break;
            }
        }
        else
        {
            query = query.OrderByDescending((er, u) => er.SubmitTime);
        }

        // 查询所有数据（不分页）- 修复准确率计算
        var studentList = await query.Select((er, u) => new CorporateTrainingExamStudentOutput
        {
            UserId = er.UserId.Value,
            RealName = u.RealName ?? u.NickName,
            Phone = u.Phone,
            TotalScore = er.TotalScore,
            Accuracy = 0, // 先设为0，后面重新计算
            DurationSeconds = er.DurationSeconds,
            SubmitTime = er.SubmitTime
        }).ToListAsync();

        // 修复准确率计算 - 导出版本
        foreach (var item in studentList)
        {
            var examResultData = await _examResultRep.AsQueryable()
                .Where(er => er.UserId == item.UserId && er.CorporateTrainingExamId == input.CorporateTrainingExamId)
                .Select(er => new { er.Id, er.TotalQuestions, er.CorrectAnswers })
                .FirstAsync();

            if (examResultData != null)
            {
                if (examResultData.TotalQuestions > 0)
                {
                    item.Accuracy = (decimal)examResultData.CorrectAnswers / examResultData.TotalQuestions * 100;
                }
                else
                {
                    // 从答题详情重新计算
                    var answerDetails = await _examAnswerRep.AsQueryable()
                        .Where(ea => ea.CorporateTrainingExamResultId == examResultData.Id)
                        .Select(ea => new { ea.IsCorrect })
                        .ToListAsync();

                    if (answerDetails.Any())
                    {
                        var totalQuestions = answerDetails.Count;
                        var correctAnswers = answerDetails.Count(ea => ea.IsCorrect);
                        item.Accuracy = totalQuestions > 0 ? (decimal)correctAnswers / totalQuestions * 100 : 0;
                    }
                }
            }
        }

        // 转换为导出DTO
        var exportList = studentList.Select(student => new ExportCorporateTrainingExamStudentDto
        {
            //UserId = student.UserId,
            RealName = student.RealName,
            Phone = student.Phone,
            TotalScore = student.TotalScore,
            Accuracy = student.Accuracy,
            DurationDisplay = student.DurationDisplay,
            SubmitTime = student.SubmitTime
        }).ToList();

        return exportList;
    }

    /// <summary>
    /// 获取线下课考试概览
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<H5ExamOverviewOutput> GetExamOverview(CorporateTrainingExamQuestionsInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证互动是否存在且为考试类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 2 && x.Status == StatusEnum.Enable);
        if (interaction == null)
            throw Oops.Oh("考试互动不存在");

        // 验证试卷是否存在
        var examPaper = await _examPaperRep.GetByIdAsync(input.ExamPaperId);
        if (examPaper == null)
            throw Oops.Oh("试卷不存在");

        // 验证是否已配置该线下课的考试
        var examConfig = await _trainingExamRep.AsQueryable()
            .Where(te => te.OfflineCourseId == input.OfflineCourseId &&
                        te.OfflineInteractionId == input.OfflineInteractionId &&
                        te.ExamPaperId == input.ExamPaperId)
            .FirstAsync();

        if (examConfig == null)
            throw Oops.Oh("该线下课尚未配置此试卷的考试");

        // 获取题目统计信息
        var questionScores = await _trainingExamQuestionRep.AsQueryable()
            .LeftJoin<ExamQuestions>((teq, q) => teq.ExamQuestionId == q.Id)
            .Where((teq, q) => teq.CorporateTrainingExamId == examConfig.Id)
            .Select((teq, q) => new ExamQuestionScoreOutput
            {
                ExamQuestionId = teq.ExamQuestionId,
                QuestionTitle = q.Title,
                TitleImage = q.TitleImage,
                QuestionType = q.QuestionType,
                QuestionScore = teq.QuestionScore
            })
            .ToListAsync();
        
        return new H5ExamOverviewOutput()
        {
             ExamName = examPaper.Name,
             TotalQuestions = questionScores.Count,
             TotalScore = questionScores.Sum(x=>x.QuestionScore)
        };
    }
    #endregion

    #region 作业相关方法

    /// <summary>
    /// 作业权限校验（客户端用）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<HomeworkPermissionOutput> CheckHomeworkPermission(GetCorporateTrainingHomeworkInput input)
    {
        var currentUserId = _userManager.UserId;
        var result = new HomeworkPermissionOutput
        {
            HasAccessPermission = false,
            IsInstructor = false,
            Message = ""
        };

        try
        {
            // 验证线下课是否存在
            var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
            if (offlineCourse == null)
            {
                result.Message = "线下课不存在";
                return result;
            }

            // 验证互动是否存在且为作业类型
            var interaction = await _interactionRep.AsQueryable()
                .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 4);
            if (interaction == null)
            {
                result.Message = "作业互动不存在";
                return result;
            }

            // 获取作业配置
            var homework = await _homeworkRep.AsQueryable()
                .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
                .FirstAsync();
            if (homework == null)
            {
                result.Message = "作业配置不存在";
                return result;
            }

            // 获取企业内训信息
            var training = await _trainingRep.AsQueryable()
                .Where(t => t.Id == offlineCourse.TrainingId)
                .FirstAsync();
            if (training == null)
            {
                result.Message = "企业内训信息不存在";
                return result;
            }
            //先检查讲师权限，再检查学员权限
            // 判断导师权限
            // 1. 如果设置了讲师手机号，检查当前用户手机号是否匹配
            var currentUser = await _userInfoRep.GetByIdAsync(currentUserId);
            if (!string.IsNullOrWhiteSpace(training.InstructorPhone))
            {
                if (currentUser != null && !string.IsNullOrWhiteSpace(currentUser.Phone))
                {
                    // 支持多个导师手机号，逗号分隔
                    var instructorPhones = training.InstructorPhone.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(phone => phone.Trim())
                        .ToArray();

                    if (instructorPhones.Contains(currentUser.Phone))
                    {
                        result.IsInstructor = true;
                        result.HasAccessPermission = true;
                        result.Message = "您是该作业的指定导师，拥有点评权限";
                        return result;
                    }
                }
            }

            //根据H5用户手机号查询管理员的账号
            var admin = await _userInfoRep.ChangeRepository<SqlSugarRepository<SysAdmin>>().AsQueryable().FirstAsync(x => x.Phone == currentUser.Phone);
            // 2. 如果没有设置讲师手机号或手机号不匹配，检查是否为作业创建人
            if (admin != null && training.CreateUserId == admin.Id)
            {
                result.IsInstructor = true;
                result.HasAccessPermission = true;
                result.Message = "您是该作业的创建人，拥有点评权限";
                return result;
            }
            // 判断非导师权限
            // 判断访问权限
            if (training.IsDesign == YesNoEnum.Y)
            {
                // 指定学员模式：检查当前用户是否在学员列表中
                var isDesignatedUser = await _trainingUserRep.AsQueryable()
                    .Where(u => u.OfflineCourseId == input.OfflineCourseId && u.UserId == currentUserId && u.Status == StatusEnum.Enable)
                    .AnyAsync();

                if (!isDesignatedUser)
                {
                    result.Message = "您不在指定学员名单中，无权访问此作业";
                    return result;
                }
            }
            else
            // 非指定学员模式：所有用户都可以访问
            {
                result.HasAccessPermission = true;
            }

            //如果是查看具体的作业详情 && 不允许学员查看其他人作业
            if (input.HomeworkSubmissionId > 0 && homework.AllowViewOthers == false) 
            {
                var submission = await _homeworkSubmissionRep.GetByIdAsync(input.HomeworkSubmissionId) ?? throw Oops.Oh("作业不存在");
                //判断是否允许学员查看其他人作业
                if (submission.UserId != currentUserId)
                {
                    result.HasAccessPermission = false;
                    result.Message = "不允许学员查看其他人作业";
                    return result;
                }
            }

            result.Message = "您有权限访问此作业，但无导师点评权限";
            return result;
        }
        catch (Exception ex)
        {
            result.Message = $"权限校验异常：{ex.Message}";
            return result;
        }
    }

    /// <summary>
    /// 配置线下课作业（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> ConfigHomework(ConfigCorporateTrainingHomeworkInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证至少启用一种作业类型
        if (!input.TextEnabled && !input.ImageEnabled && !input.VideoEnabled && !input.FileEnabled)
            throw Oops.Oh("至少需要启用一种作业类型");

        // 验证必填设置的逻辑性
        if (input.TextRequired && !input.TextEnabled)
            throw Oops.Oh("文本作业未启用，不能设置为必填");
        if (input.ImageRequired && !input.ImageEnabled)
            throw Oops.Oh("图片作业未启用，不能设置为必填");
        if (input.VideoRequired && !input.VideoEnabled)
            throw Oops.Oh("视频作业未启用，不能设置为必填");
        if (input.FileRequired && !input.FileEnabled)
            throw Oops.Oh("文件作业未启用，不能设置为必填");

        long interactionId;

        if (input.OfflineInteractionId == 0)
        {
            // 新增互动
            var newInteraction = await _interactionRep.InsertReturnEntityAsync(new OfflineInteraction()
            {
                OfflineCourseId = input.OfflineCourseId,
                Type = 4, // 作业类型（需要在OfflineInteraction中定义）
                Name = input.HomeworkName,
                QNumber = 0, // 作业没有题目数量概念
                PNumber = 0
            });
            interactionId = newInteraction.Id;
        }
        else
        {
            // 编辑现有互动
            var existingInteraction = await _interactionRep.AsQueryable()
                .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 4);
            if (existingInteraction == null)
                throw Oops.Oh("作业互动不存在");

            existingInteraction.Name = input.HomeworkName;
            await _interactionRep.UpdateAsync(existingInteraction);
            interactionId = input.OfflineInteractionId;
        }

        // 检查是否已存在配置，如果存在则更新，否则新增
        var existingHomework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == interactionId)
            .FirstAsync();

        if (existingHomework != null)
        {
            // 更新作业配置
            existingHomework.HomeworkName = input.HomeworkName;
            existingHomework.HomeworkRequirement = input.HomeworkRequirement;
            existingHomework.AllowViewOthers = input.AllowViewOthers;
            existingHomework.TextEnabled = input.TextEnabled;
            existingHomework.TextRequired = input.TextRequired;
            existingHomework.ImageEnabled = input.ImageEnabled;
            existingHomework.ImageRequired = input.ImageRequired;
            existingHomework.VideoEnabled = input.VideoEnabled;
            existingHomework.VideoRequired = input.VideoRequired;
            existingHomework.FileEnabled = input.FileEnabled;
            existingHomework.FileRequired = input.FileRequired;
            await _homeworkRep.UpdateAsync(existingHomework);
        }
        else
        {
            // 新增作业配置
            var newHomework = new CorporateTrainingHomework
            {
                OfflineCourseId = input.OfflineCourseId,
                OfflineInteractionId = interactionId,
                HomeworkName = input.HomeworkName,
                HomeworkRequirement = input.HomeworkRequirement,
                AllowViewOthers = input.AllowViewOthers,
                TextEnabled = input.TextEnabled,
                TextRequired = input.TextRequired,
                ImageEnabled = input.ImageEnabled,
                ImageRequired = input.ImageRequired,
                VideoEnabled = input.VideoEnabled,
                VideoRequired = input.VideoRequired,
                FileEnabled = input.FileEnabled,
                FileRequired = input.FileRequired
            };
            await _homeworkRep.InsertAsync(newHomework);
        }

        return true;
    }

    /// <summary>
    /// 获取线下课作业配置（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CorporateTrainingHomeworkOutput> GetHomeworkConfig(GetCorporateTrainingHomeworkInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 如果互动ID为0，表示还没有创建互动，直接返回null
        if (input.OfflineInteractionId == 0)
            return null;

        // 验证互动是否存在且为作业类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 4);
        if (interaction == null)
            throw Oops.Oh("作业互动不存在");

        // 获取作业配置
        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();

        if (homework == null)
            return null;

        // 获取提交人数
        var submissionCount = await _homeworkSubmissionRep.AsQueryable()
            .Where(s => s.CorporateTrainingHomeworkId == homework.Id)
            .CountAsync();

        return new CorporateTrainingHomeworkOutput
        {
            Id = homework.Id,
            HomeworkName = homework.HomeworkName,
            HomeworkRequirement = homework.HomeworkRequirement,
            AllowViewOthers = homework.AllowViewOthers,
            TextEnabled = homework.TextEnabled,
            TextRequired = homework.TextRequired,
            ImageEnabled = homework.ImageEnabled,
            ImageRequired = homework.ImageRequired,
            VideoEnabled = homework.VideoEnabled,
            VideoRequired = homework.VideoRequired,
            FileEnabled = homework.FileEnabled,
            FileRequired = homework.FileRequired,
            CreateTime = homework.CreateTime ?? DateTime.Now,
            SubmissionCount = submissionCount
        };
    }

    /// <summary>
    /// 获取作业详情（客户端用）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<CorporateTrainingHomeworkDetailOutput> GetHomeworkDetail(GetCorporateTrainingHomeworkInput input)
    {
        // 获取作业配置
        var homeworkConfig = await GetHomeworkConfig(input);
        if (homeworkConfig == null)
            throw Oops.Oh("作业不存在");

        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();

        // 获取当前用户的提交记录
        var currentUserId = _userManager.UserId;
        var mySubmission = await _homeworkSubmissionRep.AsQueryable().Includes(s => s.User)
            .Where(s => s.CorporateTrainingHomeworkId == homework.Id && s.UserId == currentUserId)
            .FirstAsync();

        CorporateTrainingHomeworkSubmissionOutput mySubmissionOutput = null;
        if (mySubmission != null)
        {
            // 获取我的作业点赞人信息
            var myLikeUsers = await GetHomeworkLikeUsers(mySubmission.Id);
            
            mySubmissionOutput = new CorporateTrainingHomeworkSubmissionOutput
            {
                Id = mySubmission.Id,
                CorporateTrainingHomeworkId = mySubmission.CorporateTrainingHomeworkId,
                UserId = mySubmission.UserId,
                TextContent = mySubmission.TextContent,
                ImageUrls = string.IsNullOrWhiteSpace(mySubmission.ImageUrls) ? null : mySubmission.ImageUrls.Split(',').ToList(),
                VideoUrls = string.IsNullOrWhiteSpace(mySubmission.VideoUrls) ? null : mySubmission.VideoUrls.Split(',').ToList(),
                FileUrls = string.IsNullOrWhiteSpace(mySubmission.FileUrls) ? null : mySubmission.FileUrls.Split(',').ToList(),
                StudentName = mySubmission.StudentName,
                TeacherComment = mySubmission.TeacherComment,
                TeacherCommentTime = mySubmission.TeacherCommentTime,
                CreateTime = mySubmission.CreateTime,
                UpdateTime = mySubmission.UpdateTime,
                LikeCount = mySubmission.LikeCount,
                CommentCount = mySubmission.CommentCount,
                LikeUsers = myLikeUsers,
                IsLiked = myLikeUsers.Any(x => x.UserId == currentUserId),
                Avatar = mySubmission.User.Avatar,
                NickName = mySubmission.User.NickName
            };
        }

        // 获取其他学员的提交记录（如果允许查看）
        List<CorporateTrainingHomeworkSubmissionOutput> otherSubmissions = null;
        //查询当前登录人是否为讲师
        var isInstructor = await this.IsInstructor(input.OfflineCourseId);
        if (homework.AllowViewOthers || isInstructor)
        {
            var otherSubmissionList = await _homeworkSubmissionRep.AsQueryable().Includes(s=>s.User)
                //.Where(s => s.CorporateTrainingHomeworkId == homework.Id && s.UserId != currentUserId)
                .WhereIF(isInstructor,s => s.CorporateTrainingHomeworkId == homework.Id)
                .WhereIF(!isInstructor, s => s.CorporateTrainingHomeworkId == homework.Id && s.UserId != currentUserId)
                .OrderByDescending(s => s.CreateTime)
                .ToListAsync();

            otherSubmissions = new List<CorporateTrainingHomeworkSubmissionOutput>();
            // 查询当前用户的点赞记录
            //var submissionIds = otherSubmissionList.Select(s => s.Id).ToList();
            //var userLikes = await _homeworkLikeRep.AsQueryable()
            //    .Where(l => l.UserId == currentUserId && submissionIds.Contains(l.HomeworkSubmissionId))
            //    .Select(l => l.HomeworkSubmissionId)
            //    .ToListAsync();
            foreach (var s in otherSubmissionList)
            {
                // 获取每个作业的点赞人信息
                var likeUsers = await GetHomeworkLikeUsers(s.Id);
                
                otherSubmissions.Add(new CorporateTrainingHomeworkSubmissionOutput
                {
                    Id = s.Id,
                    CorporateTrainingHomeworkId = s.CorporateTrainingHomeworkId,
                    UserId = s.UserId,
                    TextContent = s.TextContent,
                    ImageUrls = string.IsNullOrWhiteSpace(s.ImageUrls) ? null : s.ImageUrls.Split(',').ToList(),
                    VideoUrls = string.IsNullOrWhiteSpace(s.VideoUrls) ? null : s.VideoUrls.Split(',').ToList(),
                    FileUrls = string.IsNullOrWhiteSpace(s.FileUrls) ? null : s.FileUrls.Split(',').ToList(),
                    StudentName = s.StudentName,
                    TeacherComment = s.TeacherComment,
                    TeacherCommentTime = s.TeacherCommentTime,
                    CreateTime = s.CreateTime,
                    UpdateTime = s.UpdateTime,
                    LikeCount = s.LikeCount,
                    CommentCount = s.CommentCount,
                    LikeUsers = likeUsers,
                    IsLiked = likeUsers.Any(x=>x.UserId == currentUserId),
                    Avatar = s.User.Avatar,
                    NickName = s.User.NickName
                });
            }
        }

        return new CorporateTrainingHomeworkDetailOutput
        {
            HomeworkConfig = homeworkConfig,
            MySubmission = mySubmissionOutput,
            OtherSubmissions = otherSubmissions
        };
    }
    /// <summary>
    /// 是否导师
    /// </summary>
    /// <param name="offlineCourseId"></param>
    /// <returns></returns>
    private async Task<bool> IsInstructor(long offlineCourseId)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(offlineCourseId) ?? throw Oops.Oh("线下课不存在");

        // 获取企业内训信息
        var training = await _trainingRep.AsQueryable()
            .Where(t => t.Id == offlineCourse.TrainingId)
            .FirstAsync() ?? throw Oops.Oh("企业内训信息不存在");
        // 判断是否导师权限
        // 1. 如果设置了讲师手机号，检查当前用户手机号是否匹配
        if (!string.IsNullOrWhiteSpace(training.InstructorPhone))
        {
            var currentUser = await _userInfoRep.GetByIdAsync(_userManager.UserId);
            if (currentUser != null && !string.IsNullOrWhiteSpace(currentUser.Phone))
            {
                // 支持多个导师手机号，逗号分隔
                var instructorPhones = training.InstructorPhone.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(phone => phone.Trim())
                    .ToArray();

                if (instructorPhones.Contains(currentUser.Phone))
                {
                    return true;
                }
            }
        }
        return false;
    }

    /// <summary>
    /// 学员提交作业
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> SubmitHomework(CorporateTrainingHomeworkSubmitInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 验证互动是否存在且为作业类型
        var interaction = await _interactionRep.AsQueryable()
            .FirstAsync(x => x.Id == input.OfflineInteractionId && x.OfflineCourseId == input.OfflineCourseId && x.Type == 4);
        if (interaction == null)
            throw Oops.Oh("作业互动不存在");

        // 获取作业配置
        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();
        if (homework == null)
            throw Oops.Oh("作业配置不存在");
        //线下课项目参与
        var join = await App.GetService<IOfflineCourseService>().Participate(new H5OfficeCourseCheckInput()
        {
            OfflineCourseId = input.OfflineCourseId,
            OfflineInteractionId = input.OfflineInteractionId,
            HomeworkId = homework.Id
        });
        if (!join) throw Oops.Oh("线下课项目参与异常");

        // 验证必填项
        if (homework.TextRequired && string.IsNullOrWhiteSpace(input.TextContent))
            throw Oops.Oh("文本作业为必填项");
        if (homework.ImageRequired && (input.ImageUrls == null || !input.ImageUrls.Any()))
            throw Oops.Oh("图片作业为必填项");
        if (homework.VideoRequired && (input.VideoUrls == null || !input.VideoUrls.Any()))
            throw Oops.Oh("视频作业为必填项");
        if (homework.FileRequired && (input.FileUrls == null || !input.FileUrls.Any()))
            throw Oops.Oh("文件作业为必填项");

        // 验证数量限制
        if (input.ImageUrls != null && input.ImageUrls.Count > 9)
            throw Oops.Oh("图片最多上传9张");
        if (input.VideoUrls != null && input.VideoUrls.Count > 9)
            throw Oops.Oh("视频最多上传9个");
        if (input.FileUrls != null && input.FileUrls.Count > 9)
            throw Oops.Oh("文件最多上传9个");

        var currentUserId = _userManager.UserId;

        // 检查是否已经提交过
        var existingSubmission = await _homeworkSubmissionRep.AsQueryable()
            .Where(s => s.CorporateTrainingHomeworkId == homework.Id && s.UserId == currentUserId)
            .FirstAsync();
        if (existingSubmission != null)
            throw Oops.Oh("您已经提交过作业，如需修改请使用编辑功能");

        // 创建提交记录
        var submission = new CorporateTrainingHomeworkSubmission
        {
            CorporateTrainingHomeworkId = homework.Id,
            UserId = currentUserId,
            TextContent = input.TextContent,
            ImageUrls = input.ImageUrls?.Any() == true ? string.Join(",", input.ImageUrls) : null,
            VideoUrls = input.VideoUrls?.Any() == true ? string.Join(",", input.VideoUrls) : null,
            FileUrls = input.FileUrls?.Any() == true ? string.Join(",", input.FileUrls) : null,
            StudentName = input.StudentName
        };

        await _homeworkSubmissionRep.InsertAsync(submission);


        //更新互动参与人数
        await Task.Run(async () =>
        {
            await App.GetService<IOfflineCourseService>().UpdatePN(new UpdatePNOfflineInteractionInput()
            {
                InteractionId = input.OfflineInteractionId,
                HomeworkId = homework.Id
            });
        });
        return true;
    }

    /// <summary>
    /// 学员编辑作业
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> EditHomework(CorporateTrainingHomeworkEditInput input)
    {
        // 获取提交记录
        var submission = await _homeworkSubmissionRep.GetByIdAsync(input.SubmissionId);
        if (submission == null)
            throw Oops.Oh("作业提交记录不存在");

        // 验证是否为当前用户的提交
        var currentUserId = _userManager.UserId;
        if (submission.UserId != currentUserId)
            throw Oops.Oh("只能编辑自己的作业");

        // 验证是否可以编辑（导师未点评的情况下可以编辑）
        if (!string.IsNullOrWhiteSpace(submission.TeacherComment))
            throw Oops.Oh("导师已点评，无法编辑作业");

        // 获取作业配置进行验证
        var homework = await _homeworkRep.GetByIdAsync(submission.CorporateTrainingHomeworkId);
        if (homework == null)
            throw Oops.Oh("作业配置不存在");

        // 验证必填项
        if (homework.TextRequired && string.IsNullOrWhiteSpace(input.TextContent))
            throw Oops.Oh("文本作业为必填项");
        if (homework.ImageRequired && (input.ImageUrls == null || !input.ImageUrls.Any()))
            throw Oops.Oh("图片作业为必填项");
        if (homework.VideoRequired && (input.VideoUrls == null || !input.VideoUrls.Any()))
            throw Oops.Oh("视频作业为必填项");
        if (homework.FileRequired && (input.FileUrls == null || !input.FileUrls.Any()))
            throw Oops.Oh("文件作业为必填项");

        // 验证数量限制
        if (input.ImageUrls != null && input.ImageUrls.Count > 9)
            throw Oops.Oh("图片最多上传9张");
        if (input.VideoUrls != null && input.VideoUrls.Count > 9)
            throw Oops.Oh("视频最多上传9个");
        if (input.FileUrls != null && input.FileUrls.Count > 9)
            throw Oops.Oh("文件最多上传9个");

        // 更新提交记录
        submission.TextContent = input.TextContent;
        submission.ImageUrls = input.ImageUrls?.Any() == true ? string.Join(",", input.ImageUrls) : null;
        submission.VideoUrls = input.VideoUrls?.Any() == true ? string.Join(",", input.VideoUrls) : null;
        submission.FileUrls = input.FileUrls?.Any() == true ? string.Join(",", input.FileUrls) : null;
        submission.StudentName = input.StudentName;

        await _homeworkSubmissionRep.UpdateAsync(submission);
        return true;
    }

    /// <summary>
    /// 导师点评作业
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> CommentHomework(CorporateTrainingHomeworkComment1Input input)
    {
        // 获取提交记录
        var submission = await _homeworkSubmissionRep.GetByIdAsync(input.SubmissionId);
        if (submission == null)
            throw Oops.Oh("作业提交记录不存在");

        // 更新点评
        submission.TeacherComment = input.TeacherComment;
        submission.TeacherCommentTime = DateTime.Now;
        await _homeworkSubmissionRep.UpdateAsync(submission);
        return true;
    }

    /// <summary>
    /// 导师修改点评作业
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> UpdateHomeworkComment(CorporateTrainingHomeworkComment1Input input)
    {
        // 获取提交记录
        var submission = await _homeworkSubmissionRep.GetByIdAsync(input.SubmissionId);
        if (submission == null)
            throw Oops.Oh("作业提交记录不存在");

        // 验证是否已有点评（只有已点评的才能修改）
        if (string.IsNullOrWhiteSpace(submission.TeacherComment))
            throw Oops.Oh("该作业尚未点评，请使用点评功能");

        // 更新点评
        submission.TeacherComment = input.TeacherComment;
        submission.TeacherCommentTime = DateTime.Now;
        await _homeworkSubmissionRep.UpdateAsync(submission);
        return true;
    }

    /// <summary>
    /// 获取学员作业提交详情
    /// </summary>
    /// <param name="submissionId"></param>
    /// <returns></returns>
    public async Task<CorporateTrainingHomeworkSubmissionOutput> GetHomeworkSubmission(long submissionId)
    {
        var submission = await _homeworkSubmissionRep.AsQueryable()
            .Includes(s => s.User)
            .Where(s => s.Id == submissionId)
            .FirstAsync();

        if (submission == null)
            throw Oops.Oh("作业提交记录不存在");

        // 获取我的作业点赞人信息
        var myLikeUsers = await GetHomeworkLikeUsers(submissionId);

        var ret = new CorporateTrainingHomeworkSubmissionOutput
        {
            Id = submission.Id,
            CorporateTrainingHomeworkId = submission.CorporateTrainingHomeworkId,
            UserId = submission.UserId,
            TextContent = submission.TextContent,
            ImageUrls = string.IsNullOrWhiteSpace(submission.ImageUrls) ? null : submission.ImageUrls.Split(',').ToList(),
            VideoUrls = string.IsNullOrWhiteSpace(submission.VideoUrls) ? null : submission.VideoUrls.Split(',').ToList(),
            FileUrls = string.IsNullOrWhiteSpace(submission.FileUrls) ? null : submission.FileUrls.Split(',').ToList(),
            StudentName = submission.StudentName,
            TeacherComment = submission.TeacherComment,
            TeacherCommentTime = submission.TeacherCommentTime,
            CreateTime = submission.CreateTime,
            UpdateTime = submission.UpdateTime,
            LikeCount = myLikeUsers.Count,
            LikeUsers = myLikeUsers,
            CommentCount = submission.CommentCount,
            IsLiked = myLikeUsers.Any(x=>x.UserId == _userManager.UserId)
        };
        ret.ImageFiles = GetFileList(ret.ImageUrls);
        ret.VideoFiles = GetFileList(ret.VideoUrls);
        ret.FileFiles = GetFileList(ret.FileUrls);
        return ret;
    }
    /// <summary>
    /// 
    /// </summary>
    /// <param name="urls"></param>
    /// <returns></returns>
    private List<FileOutput> GetFileList(List<string> urls)
    {
        var files = new List<FileOutput>();
        if(urls != null && urls.Any())
        {
            urls.ForEach(url =>
            {
                Match match = Regex.Match(url, @"(\d+)\.\w+$");
                if (match.Success)
                {
                    var data = match.Groups[1].Value;
                    long.TryParse(data, out long id);
                    var file = _sysFileService.GetFile(id).Result;
                    files.Add(file);
                }
            });
        }
        return files;
    }

    /// <summary>
    /// 获取作业评论列表
    /// </summary>
    /// <param name="submissionId"></param>
    /// <returns></returns>
    public async Task<List<CorporateTrainingHomeworkCommentOutput>> GetHomeworkComments(long submissionId)
    {
        var currentUserId = _userManager.UserId;

        // 获取所有评论（包括回复）
        var comments = await _homeworkCommentRep.AsQueryable()
            .LeftJoin<UserInfo>((c, u) => c.UserId == u.Id)
            .Where((c, u) => c.HomeworkSubmissionId == submissionId)
            .OrderBy((c, u) => c.CreateTime)
            .Select((c, u) => new CorporateTrainingHomeworkCommentOutput
            {
                Id = c.Id,
                HomeworkSubmissionId = c.HomeworkSubmissionId,
                ParentCommentId = c.ParentCommentId,
                UserId = c.UserId,
                Content = c.Content,
                UserRole = c.UserRole,
                LikeCount = c.LikeCount,
                CreateTime = c.CreateTime,
                UserName = u.RealName ?? u.NickName,
                Avatar = u.Avatar,
                IsLiked = false // 先设为false，后面查询
            }).ToListAsync();

        // 查询当前用户对评论的点赞记录
        var commentIds = comments.Select(c => c.Id).ToList();
        var userCommentLikes = await _homeworkCommentLikeRep.AsQueryable()
            .Where(l => l.UserId == currentUserId && commentIds.Contains(l.CommentId))
            .Select(l => l.CommentId)
            .ToListAsync();

        // 设置点赞状态
        foreach (var comment in comments)
        {
            comment.IsLiked = userCommentLikes.Contains(comment.Id);
        }

        // 构建树形结构
        var rootComments = comments.Where(c => c.ParentCommentId == null || c.ParentCommentId == 0).ToList();
        var replyComments = comments.Where(c => c.ParentCommentId != null && c.ParentCommentId > 0).ToList();

        // 为每个根评论添加子评论
        foreach (var rootComment in rootComments)
        {
            BuildCommentTree(rootComment, replyComments);
        }

        return rootComments;
    }

    /// <summary>
    /// 递归构建评论树
    /// </summary>
    /// <param name="parentComment">父评论</param>
    /// <param name="allReplyComments">所有回复评论</param>
    private void BuildCommentTree(CorporateTrainingHomeworkCommentOutput parentComment, List<CorporateTrainingHomeworkCommentOutput> allReplyComments)
    {
        var childComments = allReplyComments.Where(c => c.ParentCommentId == parentComment.Id).ToList();

        foreach (var childComment in childComments)
        {
            parentComment.Children.Add(childComment);
            // 递归处理子评论的子评论
            BuildCommentTree(childComment, allReplyComments);
        }
    }

    /// <summary>
    /// 评论点赞/取消点赞
    /// </summary>
    /// <param name="commentId"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> LikeComment(long commentId)
    {
        var currentUserId = _userManager.UserId;

        // 检查评论是否存在
        var comment = await _homeworkCommentRep.GetByIdAsync(commentId);
        if (comment == null)
            throw Oops.Oh("评论不存在");

        // 检查是否已经点赞
        var existingLike = await _homeworkCommentLikeRep.AsQueryable()
            .Where(l => l.UserId == currentUserId && l.CommentId == commentId)
            .FirstAsync();

        if (existingLike != null)
        {
            // 取消点赞
            await _homeworkCommentLikeRep.DeleteAsync(existingLike);
            
            // 更新点赞数
            comment.LikeCount = Math.Max(0, comment.LikeCount - 1);
            await _homeworkCommentRep.UpdateAsync(comment);
            
            return false; // 返回false表示取消点赞
        }
        else
        {
            // 添加点赞
            var like = new CorporateTrainingHomeworkCommentLike
            {
                CommentId = commentId,
                UserId = currentUserId
            };
            await _homeworkCommentLikeRep.InsertAsync(like);
            
            // 更新点赞数
            comment.LikeCount += 1;
            await _homeworkCommentRep.UpdateAsync(comment);
            
            return true; // 返回true表示点赞成功
        }
    }

    /// <summary>
    /// 作业点赞/取消点赞
    /// </summary>
    /// <param name="submissionId"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> LikeHomework(long submissionId)
    {
        var currentUserId = _userManager.UserId;

        // 检查作业提交是否存在
        var submission = await _homeworkSubmissionRep.GetByIdAsync(submissionId);
        if (submission == null)
            throw Oops.Oh("作业提交记录不存在");

        // 检查是否已经点赞
        var existingLike = await _homeworkLikeRep.AsQueryable()
            .Where(l => l.UserId == currentUserId && l.HomeworkSubmissionId == submissionId)
            .FirstAsync();

        if (existingLike != null)
        {
            // 取消点赞
            await _homeworkLikeRep.DeleteAsync(existingLike);
            
            // 更新点赞数
            submission.LikeCount = Math.Max(0, submission.LikeCount - 1);
            await _homeworkSubmissionRep.UpdateAsync(submission);
            
            return false; // 返回false表示取消点赞
        }
        else
        {
            // 添加点赞
            var like = new CorporateTrainingHomeworkLike
            {
                HomeworkSubmissionId = submissionId,
                UserId = currentUserId
            };
            await _homeworkLikeRep.InsertAsync(like);
            
            // 更新点赞数
            submission.LikeCount += 1;
            await _homeworkSubmissionRep.UpdateAsync(submission);
            
            return true; // 返回true表示点赞成功
        }
    }

    /// <summary>
    /// 获取其他学员作业列表（按提交时间正序）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<CorporateTrainingHomeworkSubmissionListOutput>> GetHomeworkSubmissionList(GetCorporateTrainingHomeworkInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 获取作业配置
        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();
        if (homework == null)
            throw Oops.Oh("作业配置不存在");

        var currentUserId = _userManager.UserId;

        // 获取所有学员的作业提交记录（按提交时间正序）
        var submissionsData = await _homeworkSubmissionRep.AsQueryable()
            .LeftJoin<UserInfo>((s, u) => s.UserId == u.Id)
            .Where((s, u) => s.CorporateTrainingHomeworkId == homework.Id && s.UserId != currentUserId)
            .OrderBy((s, u) => s.CreateTime)
            .Select((s, u) => new
            {
                Id = s.Id,
                UserId = s.UserId,
                StudentName = s.StudentName,
                Avatar = u.Avatar,
                NickName = u.NickName,
                CreateTime = s.CreateTime,
                TextContent = s.TextContent,
                ImageUrls = s.ImageUrls,
                VideoUrls = s.VideoUrls,
                FileUrls = s.FileUrls,
                TeacherComment = s.TeacherComment,
                TeacherCommentTime = s.TeacherCommentTime,
                LikeCount = s.LikeCount,
                CommentCount = s.CommentCount
            }).ToListAsync();

        // 在内存中处理字符串分割并转换为目标对象
        var submissions = submissionsData.Select(s => new CorporateTrainingHomeworkSubmissionListOutput
        {
            Id = s.Id,
            UserId = s.UserId,
            StudentName = s.StudentName,
            Avatar = s.Avatar,
            NickName = s.NickName,
            CreateTime = s.CreateTime,
            TextContent = s.TextContent,
            ImageUrls = string.IsNullOrWhiteSpace(s.ImageUrls) ? new List<string>() : s.ImageUrls.Split(',').ToList(),
            VideoUrls = string.IsNullOrWhiteSpace(s.VideoUrls) ? new List<string>() : s.VideoUrls.Split(',').ToList(),
            FileUrls = string.IsNullOrWhiteSpace(s.FileUrls) ? new List<string>() : s.FileUrls.Split(',').ToList(),
            TeacherComment = s.TeacherComment,
            TeacherCommentTime = s.TeacherCommentTime,
            LikeCount = s.LikeCount,
            CommentCount = s.CommentCount,
            IsLiked = false
        }).ToList();

        // 查询当前用户的点赞记录
        var submissionIds = submissions.Select(s => s.Id).ToList();
        var userLikes = await _homeworkLikeRep.AsQueryable()
            .Where(l => l.UserId == currentUserId && submissionIds.Contains(l.HomeworkSubmissionId))
            .Select(l => l.HomeworkSubmissionId)
            .ToListAsync();

        // 设置点赞状态
        foreach (var submission in submissions)
        {
            submission.IsLiked = userLikes.Contains(submission.Id);
        }

        return submissions;
    }

    /// <summary>
    /// 添加作业评论
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task<bool> AddHomeworkComment(CorporateTrainingHomeworkCommentInput input)
    {
        var currentUserId = _userManager.UserId;

        // 检查作业提交是否存在
        CorporateTrainingHomeworkSubmission submission = await _homeworkSubmissionRep.GetByIdAsync(input.HomeworkSubmissionId);
        if (submission == null)
            throw Oops.Oh("作业提交记录不存在");

        // 如果是回复评论，检查父评论是否存在
        if (input.ParentCommentId.HasValue)
        {
            var parentComment = await _homeworkCommentRep.GetByIdAsync(input.ParentCommentId.Value);
            if (parentComment == null)
                throw Oops.Oh("父评论不存在");
        }

        // 确定用户角色
        var userRole = await DetermineUserRole(currentUserId, submission);

        // 创建评论
        var comment = new CorporateTrainingHomeworkComment
        {
            HomeworkSubmissionId = input.HomeworkSubmissionId,
            ParentCommentId = input.ParentCommentId,
            UserId = currentUserId,
            Content = input.Content,
            UserRole = userRole
        };

        await _homeworkCommentRep.InsertAsync(comment);

        // 更新作业的评论数
        submission.CommentCount += 1;
        await _homeworkSubmissionRep.UpdateAsync(submission);

        return true;
    }

    /// <summary>
    /// 确定用户角色
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="submission"></param>
    /// <returns></returns>
    private async Task<int> DetermineUserRole(long userId, CorporateTrainingHomeworkSubmission submission)
    {
        // 获取作业配置
        var homework = await _homeworkRep.GetByIdAsync(submission.CorporateTrainingHomeworkId);

        // 获取线下课信息
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(homework.OfflineCourseId);

        // 获取企业内训信息
        var training = await _trainingRep.AsQueryable()
            .Where(t => t.Id == offlineCourse.TrainingId)
            .FirstAsync();

        // 1-导师：检查是否为指定导师或创建人
        if (training != null)
        {
            // 1.1 检查是否为指定导师（支持多个导师手机号）
            if (!string.IsNullOrWhiteSpace(training.InstructorPhone))
            {
                var currentUser = await _trainingRep.ChangeRepository<SqlSugarRepository<UserInfo>>()
                    .GetByIdAsync(userId);
                if (currentUser != null && !string.IsNullOrWhiteSpace(currentUser.Phone))
                {
                    // 支持多个导师手机号，逗号分隔
                    var instructorPhones = training.InstructorPhone.Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(phone => phone.Trim())
                        .ToArray();

                    if (instructorPhones.Contains(currentUser.Phone))
                        return 1;
                }
            }

            // 1.2 检查是否为企业内训创建人
            if (training.CreateUserId == userId)
                return 1;
        }

        // 2-作业提交者：检查是否为作业提交人
        if (submission.UserId == userId)
            return 2;

        // 3-其他学员
        return 3;
    }

    /// <summary>
    /// 获取作业点赞用户信息
    /// </summary>
    /// <param name="submissionId"></param>
    /// <returns></returns>
    private async Task<List<HomeworkLikeUserOutput>> GetHomeworkLikeUsers(long submissionId)
    {
        var result = await _homeworkLikeRep.AsQueryable()
            .LeftJoin<UserInfo>((l, u) => l.UserId == u.Id)
            //.LeftJoin<CorporateTrainingHomeworkSubmission>((l, u, s) => l.HomeworkSubmissionId == s.Id)
            .Where((l, u) => l.HomeworkSubmissionId == submissionId)
            .Select((l, u) => new HomeworkLikeUserOutput
            {
                UserId = l.UserId,
                UserName = u.RealName ?? u.NickName,//s.StudentName ?? 这里StudentName只是提交作业的学员名称
                Avatar = u.Avatar,
                CreateTime = l.CreateTime
            })
            .ToListAsync();

        return result.OrderByDescending(x => x.CreateTime).ToList();
    }

    /// <summary>
    /// 获取课后作业分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<HomeworkSubmissionPageOutput>> GetHomeworkSubmissionPage(HomeworkSubmissionPageInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 获取作业配置
        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();
        if (homework == null)
            throw Oops.Oh("作业配置不存在");

        // 获取企业内训信息，判断是否指定学员
        var corporateTraining = await _trainingRep.AsQueryable()
            .Where(ct => ct.Id == offlineCourse.TrainingId)
            .FirstAsync();

        // 判断是否显示部门和职位（只有企业内训且指定学员时才显示）
        bool showDepartmentAndPosition = corporateTraining != null && corporateTraining.IsDesign == YesNoEnum.Y;

        // 构建查询
        var query = _homeworkSubmissionRep.AsQueryable()
            .LeftJoin<UserInfo>((s, u) => s.UserId == u.Id)
            .LeftJoin<OfflineCourseUser>((s, u, ocu) => s.UserId == ocu.UserId && ocu.OfflineCourseId == input.OfflineCourseId)
            .Where((s, u, ocu) => s.CorporateTrainingHomeworkId == homework.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), (s, u, ocu) => u.RealName.Contains(input.RealName) || ocu.Name.Contains(input.RealName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), (s, u, ocu) => u.Phone.Contains(input.Phone) || ocu.Phone.Contains(input.Phone))
            .WhereIF(input.IsCommented.HasValue && input.IsCommented.Value, (s, u, ocu) => !string.IsNullOrWhiteSpace(s.TeacherComment))
            .WhereIF(input.IsCommented.HasValue && !input.IsCommented.Value, (s, u, ocu) => string.IsNullOrWhiteSpace(s.TeacherComment))
            .OrderByDescending((s, u, ocu) => s.CreateTime);

        // 分页查询
        var result = await query.Select((s, u, ocu) => new HomeworkSubmissionPageOutput
        {
            Id = s.Id,
            RealName = SqlFunc.IsNull(s.StudentName, u.RealName),
            Phone = SqlFunc.IsNull(ocu.Phone, u.Phone),
            Department = showDepartmentAndPosition ? ocu.Department : null,
            Position = showDepartmentAndPosition ? ocu.Position : null,
            TextContent = s.TextContent,
            SubmitTime = s.CreateTime,
            IsCommented = !string.IsNullOrWhiteSpace(s.TeacherComment),
            CommentStatus = string.IsNullOrWhiteSpace(s.TeacherComment) ? "待点评" : "已点评"
        }).ToPagedListAsync(input.Page, input.PageSize);

        return result;
    }

    /// <summary>
    /// 获取课后作业导出列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<(List<ExportHomeworkSubmissionDto>, string)> GetHomeworkSubmissionExportList(HomeworkSubmissionPageInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 获取作业配置
        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();
        if (homework == null)
            throw Oops.Oh("作业配置不存在");

        // 获取企业内训信息，判断是否指定学员
        var corporateTraining = await _trainingRep.AsQueryable()
            .Where(ct => ct.Id == offlineCourse.TrainingId)
            .FirstAsync();

        // 判断是否显示部门和职位（只有企业内训且指定学员时才显示）
        bool showDepartmentAndPosition = corporateTraining != null && corporateTraining.IsDesign == YesNoEnum.Y;

        // 构建查询（不分页，获取所有数据）
        var query = _homeworkSubmissionRep.AsQueryable()
            .LeftJoin<UserInfo>((s, u) => s.UserId == u.Id)
            .LeftJoin<OfflineCourseUser>((s, u, ocu) => s.UserId == ocu.UserId && ocu.OfflineCourseId == input.OfflineCourseId)
            .Where((s, u, ocu) => s.CorporateTrainingHomeworkId == homework.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), (s, u, ocu) => u.RealName.Contains(input.RealName) || ocu.Name.Contains(input.RealName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), (s, u, ocu) => u.Phone.Contains(input.Phone) || ocu.Phone.Contains(input.Phone))
            .WhereIF(input.IsCommented.HasValue && input.IsCommented.Value, (s, u, ocu) => !string.IsNullOrWhiteSpace(s.TeacherComment))
            .WhereIF(input.IsCommented.HasValue && !input.IsCommented.Value, (s, u, ocu) => string.IsNullOrWhiteSpace(s.TeacherComment))
            .OrderByDescending((s, u, ocu) => s.CreateTime);

        // 查询所有数据
        var list = await query.Select((s, u, ocu) => new ExportHomeworkSubmissionDto
        {
            RealName = SqlFunc.IsNull(ocu.Name, u.RealName),
            Phone = SqlFunc.IsNull(ocu.Phone, u.Phone),
            Department = showDepartmentAndPosition ? ocu.Department : null,
            Position = showDepartmentAndPosition ? ocu.Position : null,
            TextContent = s.TextContent,
            SubmitTime = s.CreateTime.Value,
            CommentStatus = string.IsNullOrWhiteSpace(s.TeacherComment) ? "待点评" : "已点评"
        }).ToListAsync();

        // 生成文件名：作业名称 + 日期
        string fileName = $"{homework.HomeworkName}_{DateTime.Now:yyyyMMdd}";

        return (list, fileName);
    }

    /// <summary>
    /// 导出课后作业文件压缩包
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<IActionResult> ExportHomeworkFiles(HomeworkSubmissionPageInput input)
    {
        // 验证线下课是否存在
        var offlineCourse = await _offlineCourseRep.GetByIdAsync(input.OfflineCourseId);
        if (offlineCourse == null)
            throw Oops.Oh("线下课不存在");

        // 获取作业配置
        var homework = await _homeworkRep.AsQueryable()
            .Where(h => h.OfflineCourseId == input.OfflineCourseId && h.OfflineInteractionId == input.OfflineInteractionId)
            .FirstAsync();
        if (homework == null)
            throw Oops.Oh("作业配置不存在");

        // 获取企业内训信息，判断是否指定学员
        var corporateTraining = await _trainingRep.AsQueryable()
            .Where(ct => ct.Id == offlineCourse.TrainingId)
            .FirstAsync();

        // 构建查询获取作业提交数据
        var query = _homeworkSubmissionRep.AsQueryable()
            .LeftJoin<UserInfo>((s, u) => s.UserId == u.Id)
            .LeftJoin<OfflineCourseUser>((s, u, ocu) => s.UserId == ocu.UserId && ocu.OfflineCourseId == input.OfflineCourseId)
            .Where((s, u, ocu) => s.CorporateTrainingHomeworkId == homework.Id)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), (s, u, ocu) => u.RealName.Contains(input.RealName) || ocu.Name.Contains(input.RealName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Phone), (s, u, ocu) => u.Phone.Contains(input.Phone) || ocu.Phone.Contains(input.Phone))
            .WhereIF(input.IsCommented.HasValue && input.IsCommented.Value, (s, u, ocu) => !string.IsNullOrWhiteSpace(s.TeacherComment))
            .WhereIF(input.IsCommented.HasValue && !input.IsCommented.Value, (s, u, ocu) => string.IsNullOrWhiteSpace(s.TeacherComment))
            .OrderByDescending((s, u, ocu) => s.CreateTime);

        // 查询所有数据，包含文件信息
        var submissions = await query.Select((s, u, ocu) => new
        {
            RealName = SqlFunc.IsNull(ocu.Name, u.RealName),
            Phone = SqlFunc.IsNull(ocu.Phone, u.Phone),
            ImageUrls = s.ImageUrls,
            VideoUrls = s.VideoUrls,
            FileUrls = s.FileUrls
        }).ToListAsync();

        if (!submissions.Any())
        {
            throw Oops.Oh("没有找到作业提交记录");
        }

        // 创建内存流用于压缩包
        var memoryStream = new MemoryStream();
        using (var zipArchive = new ZipArchive(memoryStream, ZipArchiveMode.Create, true))
        {
            foreach (var submission in submissions)
            {
                // 创建学员文件夹名称：姓名+手机号
                var folderName = $"{submission.RealName}_{submission.Phone}";

                // 处理图片文件
                if (!string.IsNullOrWhiteSpace(submission.ImageUrls))
                {
                    var imageUrls = submission.ImageUrls.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    await AddFilesToZip(zipArchive, imageUrls, folderName, "images");
                }

                // 处理视频文件
                if (!string.IsNullOrWhiteSpace(submission.VideoUrls))
                {
                    var videoUrls = submission.VideoUrls.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    await AddFilesToZip(zipArchive, videoUrls, folderName, "videos");
                }

                // 处理其他文件
                if (!string.IsNullOrWhiteSpace(submission.FileUrls))
                {
                    var fileUrls = submission.FileUrls.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    await AddFilesToZip(zipArchive, fileUrls, folderName, "files");
                }
            }
        }

        memoryStream.Position = 0;

        // 生成压缩包文件名：课程名称
        var courseName = offlineCourse.Name ?? homework.HomeworkName;
        // 清理文件名中的特殊字符，确保文件名安全
        var safeCourseName = System.Text.RegularExpressions.Regex.Replace(courseName, @"[<>:""/\\|?*]", "_");
        var zipFileName = $"{safeCourseName}_{DateTime.Now:yyyyMMdd}.zip";

        return new FileStreamResult(memoryStream, "application/zip")
        {
            FileDownloadName = zipFileName
        };
    }

    /// <summary>
    /// 将文件添加到压缩包中
    /// </summary>
    /// <param name="zipArchive">压缩包</param>
    /// <param name="fileUrls">文件URL数组</param>
    /// <param name="folderName">文件夹名称</param>
    /// <param name="subFolder">子文件夹名称</param>
    /// <returns></returns>
    private async Task AddFilesToZip(ZipArchive zipArchive, string[] fileUrls, string folderName, string subFolder)
    {
        for (int i = 0; i < fileUrls.Length; i++)
        {
            var fileUrl = fileUrls[i].Trim();
            if (string.IsNullOrWhiteSpace(fileUrl)) continue;

            try
            {
                // 从URL中提取文件路径和文件名
                var fileName = GetFileNameFromUrl(fileUrl, subFolder, i + 1);
                var filePath = GetFilePathFromUrl(fileUrl);

                // 在压缩包中创建文件路径：学员文件夹/子文件夹/文件名
                var entryName = $"{folderName}/{subFolder}/{fileName}";

                // 记录开始下载
                var startTime = DateTime.Now;
                Console.WriteLine($"开始下载文件: {fileName}, 路径: {filePath}");

                // 下载文件内容
                byte[] fileBytes = null;
                if (_OSSProviderOptions.IsEnable && _OSSService != null)
                {
                    try
                    {
                        // 从OSS下载文件 - 使用流式下载避免内存问题
                        var presignedResult = await _OSSService.PresignedGetObjectAsync(_OSSProviderOptions.Bucket, filePath, 30); // 增加签名有效期到30分钟

                        // 对于大文件，使用流式下载而不是一次性加载到内存
                        using (var httpClient = new HttpClient())
                        {
                            // 设置更长的超时时间
                            httpClient.Timeout = TimeSpan.FromMinutes(10);

                            using (var response = await httpClient.GetAsync(presignedResult.ToString()))
                            {
                                response.EnsureSuccessStatusCode();

                                // 获取文件大小
                                var contentLength = response.Content.Headers.ContentLength ?? 0;
                                Console.WriteLine($"文件大小: {contentLength / (1024 * 1024):F2} MB");

                                // 如果文件过大（超过500MB），跳过处理
                                if (contentLength > 500 * 1024 * 1024)
                                {
                                    Console.WriteLine($"文件过大，跳过处理: {fileName} ({contentLength / (1024 * 1024):F2} MB)");
                                    continue;
                                }

                                fileBytes = await response.Content.ReadAsByteArrayAsync();
                            }
                        }
                    }
                    catch (HttpRequestException httpEx)
                    {
                        Console.WriteLine($"HTTP请求失败: {fileName}, 错误: {httpEx.Message}");
                        continue;
                    }
                    catch (TaskCanceledException timeoutEx)
                    {
                        Console.WriteLine($"下载超时: {fileName}, 错误: {timeoutEx.Message}");
                        continue;
                    }
                }
                else
                {
                    // 从本地文件系统读取文件
                    var localFilePath = Path.Combine(App.WebHostEnvironment.WebRootPath, filePath);
                    if (File.Exists(localFilePath))
                    {
                        var fileInfo = new System.IO.FileInfo(localFilePath);
                        Console.WriteLine($"本地文件大小: {fileInfo.Length / (1024 * 1024):F2} MB");

                        // 检查文件大小
                        if (fileInfo.Length > 500 * 1024 * 1024)
                        {
                            Console.WriteLine($"本地文件过大，跳过处理: {fileName} ({fileInfo.Length / (1024 * 1024):F2} MB)");
                            continue;
                        }

                        fileBytes = await File.ReadAllBytesAsync(localFilePath);
                    }
                    else
                    {
                        Console.WriteLine($"本地文件不存在: {localFilePath}");
                        continue;
                    }
                }

                if (fileBytes != null && fileBytes.Length > 0)
                {
                    // 创建压缩包条目
                    var entry = zipArchive.CreateEntry(entryName);
                    using (var entryStream = entry.Open())
                    {
                        await entryStream.WriteAsync(fileBytes, 0, fileBytes.Length);
                    }

                    var endTime = DateTime.Now;
                    var duration = endTime - startTime;
                    Console.WriteLine($"文件下载成功: {fileName}, 大小: {fileBytes.Length / (1024 * 1024):F2} MB, 耗时: {duration.TotalSeconds:F2} 秒");
                }
                else
                {
                    Console.WriteLine($"文件下载失败或文件为空: {fileName}");
                }
            }
            catch (Exception ex)
            {
                // 记录详细错误信息
                Console.WriteLine($"处理文件失败: {fileUrl}");
                Console.WriteLine($"错误类型: {ex.GetType().Name}");
                Console.WriteLine($"错误消息: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 如果有内部异常，也记录
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
            }
        }
    }

    /// <summary>
    /// 从URL中提取文件名
    /// </summary>
    /// <param name="url">文件URL或路径</param>
    /// <param name="fileType">文件类型（images/videos/files）</param>
    /// <param name="index">文件索引</param>
    /// <returns>文件名</returns>
    private string GetFileNameFromUrl(string url, string fileType = "", int index = 1)
    {
        try
        {
            // 直接从路径中提取文件名，支持相对路径和绝对路径
            var fileName = Path.GetFileName(url);

            // 如果文件名为空或没有扩展名，根据文件类型生成默认文件名
            if (string.IsNullOrWhiteSpace(fileName) || !Path.HasExtension(fileName))
            {
                var extension = GetDefaultExtensionByType(fileType, url);
                var baseName = string.IsNullOrWhiteSpace(fileName) ? $"file_{index}" : Path.GetFileNameWithoutExtension(fileName);
                return $"{baseName}{extension}";
            }

            return fileName;
        }
        catch
        {
            var extension = GetDefaultExtensionByType(fileType, url);
            return $"file_{index}{extension}";
        }
    }

    /// <summary>
    /// 根据文件类型获取默认扩展名
    /// </summary>
    /// <param name="fileType">文件类型（images/videos/files）</param>
    /// <param name="url">原始URL或路径，用于尝试推断扩展名</param>
    /// <returns>文件扩展名</returns>
    private string GetDefaultExtensionByType(string fileType, string url)
    {
        // 首先尝试从路径中推断扩展名
        try
        {
            var extension = Path.GetExtension(url);
            if (!string.IsNullOrWhiteSpace(extension))
            {
                return extension;
            }
        }
        catch { }

        // 根据文件类型返回默认扩展名
        return fileType.ToLower() switch
        {
            "images" => ".jpg",
            "videos" => ".mp4",
            "files" => ".pdf",
            _ => ".file"
        };
    }

    /// <summary>
    /// 从URL中提取文件路径（用于OSS或本地文件系统）
    /// </summary>
    /// <param name="url">文件URL</param>
    /// <returns>文件路径</returns>
    private string GetFilePathFromUrl(string url)
    {
        try
        {
            if (_OSSProviderOptions.IsEnable)
            {
                // OSS URL格式：https://bucket.endpoint/path/to/file
                var uri = new Uri(url);
                return uri.LocalPath.TrimStart('/');
            }
            else
            {
                // 本地URL格式：/upload/path/to/file
                var uri = new Uri(url, UriKind.RelativeOrAbsolute);
                if (uri.IsAbsoluteUri)
                {
                    return uri.LocalPath.TrimStart('/');
                }
                else
                {
                    return url.TrimStart('/');
                }
            }
        }
        catch
        {
            return url.TrimStart('/');
        }
    }

    #endregion

    /// <summary>
    /// 选择作业点评人员（用于伪登录）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SelectHomeworkReviewerOutput> SelectHomeworkReviewerAsync(SelectHomeworkReviewerInput input)
    {
        // 获取企业内训信息
        var training = await _trainingRep.GetByIdAsync(input.TrainingId);
        if (training == null)
            throw Oops.Oh("企业内训不存在");

        // 1. 首先检查是否有导师手机号，如果有则查找对应的用户账户
        if (!string.IsNullOrWhiteSpace(training.InstructorPhone))
        {
            // 支持多个导师手机号，逗号分隔
            var instructorPhones = training.InstructorPhone.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(phone => phone.Trim())
                .ToArray();

            // 查找所有导师对应的用户账户
            var instructorUsers = await _trainingRep.ChangeRepository<SqlSugarRepository<UserInfo>>()
                .AsQueryable()
                .Where(u => instructorPhones.Contains(u.Phone) && u.Status == StatusEnum.Enable)
                .ToListAsync();

            if (instructorUsers.Any())
            {
                // 如果找到导师账户，随机选择一个
                var random = new Random();
                var selectedUser = instructorUsers[random.Next(instructorUsers.Count)];

                return new SelectHomeworkReviewerOutput
                {
                    UserId = selectedUser.Id,
                    Phone = selectedUser.Phone,
                    NickName = selectedUser.NickName,
                    RealName = selectedUser.RealName,
                    SelectType = 1,
                    Remark = $"从{instructorUsers.Count}个导师账户中随机选择"
                };
            }
        }

        // 2. 如果没有找到导师账户，则选择创建人账户
        if (training.CreateUserId.HasValue)
        {
            var creatorAdmin = await _trainingRep.ChangeRepository<SqlSugarRepository<SysAdmin>>()
                .GetByIdAsync(training.CreateUserId.Value);

            if (creatorAdmin != null)
            {
                var creatorUser = await _userInfoRep.AsQueryable().FirstAsync(x=> x.Phone == creatorAdmin.Phone);
                if (creatorUser != null && creatorUser.Status == StatusEnum.Enable)
                {
                    return new SelectHomeworkReviewerOutput
                    {
                        UserId = creatorUser.Id,
                        Phone = creatorUser.Phone,
                        NickName = creatorUser.NickName,
                        RealName = creatorUser.RealName,
                        SelectType = 2,
                        Remark = "选择企业内训创建人账户"
                    };
                }
            }
        }

        // 3. 如果创建人账户也不存在或不可用，抛出异常
        throw Oops.Oh("未找到可用的点评人员账户，请检查导师手机号设置或创建人信息");
    }

}
