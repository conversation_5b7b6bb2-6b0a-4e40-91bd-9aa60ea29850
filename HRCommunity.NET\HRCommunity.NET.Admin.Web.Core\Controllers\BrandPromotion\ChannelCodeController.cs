﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 营销-渠道码管理
/// </summary>
[ApiDescriptionSettings("营销-渠道码管理", Order = 480)]
public class ChannelCodeController : IDynamicApiController
{
    private readonly IChannelCodeService _channelCodeService;
    public ChannelCodeController(IChannelCodeService channelCodeService)
    {
        _channelCodeService = channelCodeService;
    }

    /// <summary>
    /// 列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取渠道码分页列表")]
    public async Task<SqlSugarPagedList<ChannelCodePageOutput>> Page(ChannelCodePageInput input)
    {
        return await _channelCodeService.Page(input);
    }

    /// <summary>
    /// 增加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加渠道码")]
    public async Task<bool> Add(AddChannelCodeInput input)
    {
        var id = await _channelCodeService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新渠道码")]
    public async Task<bool> Update(UpdateChannelCodeInput input)
    {
        return await _channelCodeService.Update(input);
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task<bool> SetStatus(ChannelCodeInput input)
    {
        return await _channelCodeService.SetStatus(input);
    }

}
