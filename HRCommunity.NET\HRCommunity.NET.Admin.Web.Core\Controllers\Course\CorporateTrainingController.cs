﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Magicodes.IE.Excel;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;
/// <summary>
/// 企业内训服务
/// </summary>
[ApiDescriptionSettings("企业内训服务", Order = 400)]
public class CorporateTrainingController : IDynamicApiController
{
    private readonly ICorporateTrainingService _trainingService;
    private readonly IOfflineCourseService _offlineCourseService;
    private readonly INoahHelper _iNoahHelper;
    public CorporateTrainingController(ICorporateTrainingService trainingService,
                                        IOfflineCourseService offlineCourseService,
                                        INoahHelper iNoahHelper)
    {
        _trainingService = trainingService;
        _offlineCourseService = offlineCourseService;
        _iNoahHelper = iNoahHelper;
    }

    /// <summary>
    /// 获取分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取分页列表")]
    public async Task<SqlSugarPagedList<PageCorporateTrainingOutput>> Page(PageCorporateTrainingInput input)
    {
        return await _trainingService.Page(input);
    }

    /// <summary>
    /// 获取详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取详情")]
    public async Task<CorporateTraining> Detail(BaseIdInput input)
    {
        return await _trainingService.GetDetail(input.Id);
    }

    /// <summary>
    /// 保存信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("保存信息")]
    public async Task<bool> Save(AddCorporateTrainingInput input)
    {
        var (isSuccess,id) = await _trainingService.Save(input);
        if (isSuccess)
        {
            //保存成功，创建线下课数据
            await Task.Run(async () =>
            {
                await _offlineCourseService.GetOrCreate(new OfflineCourseDetailInput()
                {
                    TrainingId = id
                });
            });
        }
        return isSuccess;
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task<bool> SetStatus(CorporateTrainingStatusInput input)
    {
        return await _trainingService.SetStatus(input);
    }

    /// <summary>
    /// 根据项目编号查询项目信息
    /// </summary>
    /// <param name="Code"></param>
    /// <returns></returns>
    [DisplayName("根据项目编号查询项目信息")]
    public async Task<ProjectOutput> GetProjectInfoByCode(string Code)
    {
        var project = await _iNoahHelper.GetProjectInfoByCode(Code);
        if (project == null || project.Code != 200 || project.Data == null)
            throw Oops.Oh("请输入正确的项目编号");
        return new ProjectOutput()
        {
            EnterpriseName = project.Data.CustomerName,
            ProjectName = project.Data.ProjectName,
            ProjectNo = project.Data.ProjectCode,
            ProjectManager = project.Data.BeneficiaryName,
            InstructorName = project.Data.InstructorList != null ? string.Join('；', project.Data.InstructorList.Select(x => x.TeacherName)) : ""
        };
    }

    

    #region 线下课考试（支持课程、企业内训、公开课）

    /// <summary>
    /// 获取试卷题目列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取试卷题目列表")]
    public async Task<List<ExamPaperQuestionOutput>> ExamPaperQuestions(GetExamPaperQuestionsInput input)
    {
        return await _trainingService.GetExamPaperQuestions(input);
    }

    /// <summary>
    /// 配置线下课考试（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("配置线下课考试")]
    public async Task<bool> ConfigExam(ConfigCorporateTrainingExamInput input)
    {
        return await _trainingService.ConfigExam(input);
    }

    /// <summary>
    /// 获取线下课考试配置（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取线下课考试配置")]
    public async Task<CorporateTrainingExamOutput> ExamConfig(GetCorporateTrainingExamInput input)
    {
        return await _trainingService.GetExamConfig(input);
    }

    /// <summary>
    /// 获取线下课考试基本信息分页（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取线下课考试基本信息分页")]
    public async Task<SqlSugarPagedList<CorporateTrainingExamBasicInfoOutput>> ExamBasicInfoPage(CorporateTrainingExamBasicInfoPageInput input)
    {
        return await _trainingService.GetExamBasicInfoPage(input);
    }

    /// <summary>
    /// 获取线下课考试学员信息分页（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取线下课考试学员信息分页")]
    public async Task<SqlSugarPagedList<CorporateTrainingExamStudentOutput>> ExamStudentPage(CorporateTrainingExamStudentPageInput input)
    {
        return await _trainingService.GetExamStudentPage(input);
    }

    /// <summary>
    /// 导出线下课考试学员信息（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportExamStudent"), NonUnify]
    [DisplayName("导出线下课考试学员信息")]
    public async Task<IActionResult> ExportExamStudent(CorporateTrainingExamStudentPageInput input)
    {
        var list = await _trainingService.GetExamStudentExportList(input);

        var excelExporter = new ExcelExporter();

        var res = await excelExporter.ExportAsByteArray(list);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream")
        {
            FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + "线下课考试学员信息.xlsx"
        };
    }

    #endregion

    #region 线下课作业（支持课程、企业内训、公开课）

    /*
     {
  "offlineCourseId": 21654416635589,
  "offlineInteractionId": 21830944832965,
  "homeworkName": "测试作业名称",
  "homeworkRequirement": "必须上传文件以及作业写作步骤",
  "allowViewOthers": true,
  "textEnabled": true,
  "textRequired": true,
  "imageEnabled": true,
  "imageRequired": true,
  "videoEnabled": true,
  "videoRequired": true,
  "fileEnabled": true,
  "fileRequired": true
}
     */
    /// <summary>
    /// 配置线下课作业（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("配置线下课作业")]
    public async Task<bool> ConfigHomework(ConfigCorporateTrainingHomeworkInput input)
    {
        return await _trainingService.ConfigHomework(input);
    }

    /// <summary>
    /// 获取线下课作业配置（支持课程、企业内训、公开课）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取线下课作业配置")]
    public async Task<CorporateTrainingHomeworkOutput> HomeworkConfig(GetCorporateTrainingHomeworkInput input)
    {
        return await _trainingService.GetHomeworkConfig(input);
    }

    /// <summary>
    /// 获取学员作业提交详情（运营后台查看）
    /// </summary>
    /// <param name="submissionId"></param>
    /// <returns></returns>
    [DisplayName("获取学员作业提交详情")]
    public async Task<CorporateTrainingHomeworkSubmissionOutput> HomeworkSubmissionDetail(long submissionId)
    {
        return await _trainingService.GetHomeworkSubmission(submissionId);
    }

    /// <summary>
    /// 导师点评作业
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导师点评作业")]
    public async Task<bool> CommentHomework(CorporateTrainingHomeworkComment1Input input)
    {
        return await _trainingService.CommentHomework(input);
    }

    /// <summary>
    /// 获取课后作业分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取课后作业分页列表")]
    public async Task<SqlSugarPagedList<HomeworkSubmissionPageOutput>> HomeworkSubmissionPage(HomeworkSubmissionPageInput input)
    {
        return await _trainingService.GetHomeworkSubmissionPage(input);
    }

    /// <summary>
    /// 导出课后作业提交信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportHomeworkSubmission"), NonUnify]
    [DisplayName("导出课后作业提交信息")]
    public async Task<IActionResult> ExportHomeworkSubmission(HomeworkSubmissionPageInput input)
    {
        var (list, fileName) = await _trainingService.GetHomeworkSubmissionExportList(input);

        var excelExporter = new ExcelExporter();
        var res = await excelExporter.ExportAsByteArray(list);

        return new FileStreamResult(new MemoryStream(res), "application/octet-stream")
        {
            FileDownloadName = $"{fileName}.xlsx"
        };
    }

    /// <summary>
    /// 导出课后作业文件压缩包
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportHomeworkFiles"), NonUnify]
    [DisplayName("导出课后作业文件压缩包")]
    public async Task<IActionResult> ExportHomeworkFiles(HomeworkSubmissionPageInput input)
    {
        return await _trainingService.ExportHomeworkFiles(input);
    }

    /// <summary>
    /// 获取作业点评人员ID（用于伪登录）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取作业点评人员ID")]
    public async Task<SelectHomeworkReviewerOutput> SelectHomeworkReviewer(SelectHomeworkReviewerInput input)
    {
        return await _trainingService.SelectHomeworkReviewerAsync(input);
    }
    #endregion
}
