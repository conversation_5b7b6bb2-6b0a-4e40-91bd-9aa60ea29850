{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "NuoPinConfig": { //诺聘请求接口配置
    "ApiUrl": "https://openapi.nuopin.cn/",//此处主要是生成短链使用，诺聘只有线上环境才能生成有效短链
    "ClientId": "6030519482000010006",
    "ClientSecret": "2F031256C24542339C36597E1F381876"
  },
  "BesChannelsConfig": { //致趣百川配置
    "OpenApiUrl": "https://openapi.beschannels.com", // 致趣百川openai地址
    "AppId": "zq_b4a83f17", // AppId
    "AppSecret": "B63FCB3153702C4B6DC7DF8E9BE8877A", // 密钥
    "WxAppId": "wx855da4ac71b82cc5" // 公众号的appid
  },
  "INoahConfig": { //数字诺亚请求接口配置
    "TokenApiUrl": "http://auth.hbhro.com/oauth/token", //授权apiurl
    "ProjectApiUrl": "https://ehr.hbhro.com/project/novapin", //项目apiurl
    "ClientId": "hrshequ",
    "ClientSecret": "hrshequ"
  },
  "NoahShortConfig": {
    "ApiUrl": "https://dwz.noahhr.cn", //诺亚短网址网站https://dwz.noahhr.cn，生成短网址接口
    "Secret": "dwznoahhr!@#"
  }
}