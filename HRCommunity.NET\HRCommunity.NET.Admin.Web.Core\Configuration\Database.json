{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

  "DbConnection": {
    // 具体配置见SqlSugar官网（第一个为默认库不需要设置ConfigId）
    "ConnectionConfigs": [
      {
        "DbType": "SqlServer", // MySql、SqlServer、Sqlite、Oracle、PostgreSQL、Dm、Kdbndp、Oscar、MySqlConnector、Access、OpenGauss、QuestDB、HG、ClickHouse、GBase、Odbc、Custom
        //"ConnectionString": "Data Source=.;Initial Catalog=HRCommunity.NET;Persist Security Info=True;User ID=sa;Password=***;", //"DataSource=./Admin.NET.db", // 库连接字符串
        //"ConnectionString": "Data Source=localhost\\SQLEXPRESS;Initial Catalog=HRCommunity.NET;Persist Security Info=True;User ID=sa;Password=***;", //"DataSource=./Admin.NET.db", // 库连接字符串
        "ConnectionString": "Data Source=rm-2ze11s93y319855go7o.sqlserver.rds.aliyuncs.com,3433;Initial Catalog=HRCommunity.NET.Test;Persist Security Info=True;User ID=HrResearchTest;Password=HrResearchTest2023!@#; Max Pool Size=5120000;", // 库连接字符串
        //"ConnectionString": "Data Source=rm-2ze11s93y319855go7o.sqlserver.rds.aliyuncs.com,3433;Initial Catalog=HRCommunity.NET;Persist Security Info=True;User ID=hrecosphere2021;Password=***********$@; Max Pool Size=5120000;", // 库连接字符串
        "EnableInitDb": false, // 启用库表初始化
        "EnableInitSeed": false, // 启用种子初始化
        "EnableDiffLog": false, // 启用库表差异日志
        "EnableUnderLine": false // 启用驼峰转下划线
      }
      //"ConnectionConfigs",
      //:,
      //[
      //  {
      //    "DbType": "MySql", // MySql、SqlServer、Sqlite、Oracle、PostgreSQL、Dm、Kdbndp、Oscar、MySqlConnector、Access、OpenGauss、QuestDB、HG、ClickHouse、GBase、Odbc、Custom
      //    "ConnectionString": "Data Source=localhost;port=3306;Initial Catalog=HRCommunity.NET;Charset=utf8mb4;user id=root;password=******", // 库连接字符串
      //    "EnableInitDb": true, // 启用库表初始化
      //    "EnableInitSeed": true, // 启用种子初始化
      //    "EnableDiffLog": false, // 启用库表差异日志
      //    "EnableUnderLine": false // 启用驼峰转下划线
      //  }
      // 其他数据库配置（可以配置多个）
      //{
      //    "ConfigId": "test",
      //    "DbType": "Sqlite",
      //    "ConnectionString": "DataSource=./test.db", // 库连接字符串
      //    "EnableInitDb": true, // 启用库表初始化
      //    "EnableInitSeed": true, // 启用种子初始化
      //    "EnableDiffLog": false, // 启用库表差异日志
      //    "EnableUnderLine": false // 启用驼峰转下划线
      //}
    ]
  }
}