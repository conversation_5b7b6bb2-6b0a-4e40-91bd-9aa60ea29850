﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 营销-广告管理
/// </summary>
[ApiDescriptionSettings("营销-广告管理", Order = 500)]
public class AdvertisementController : IDynamicApiController
{
    private readonly IAdvertisementService _advertisementService;
    public AdvertisementController(IAdvertisementService advertisementService)
    {
        _advertisementService = advertisementService;
    }

    /// <summary>
    /// 列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取广告分页列表")]
    public async Task<SqlSugarPagedList<AdvertisementPageOutput>> Page(AdvertisementPageInput input)
    {
        return await _advertisementService.Page(input);
    }

    /// <summary>
    /// 增加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加广告")]
    public async Task<bool> Add(AddAdvertisementInput input)
    {
        var id = await _advertisementService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新广告")]
    public async Task<bool> Update(UpdateAdvertisementInput input)
    {
        return await _advertisementService.Update(input);
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task<bool> SetStatus(AdvertisementInput input)
    {
        return await _advertisementService.SetStatus(input);
    }

    /// <summary>
    /// 删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除")]
    public async Task<bool> Del(BaseIdInput input)
    {
        return await _advertisementService.Del(input);
    }



    /// <summary>
    /// 数据-统计列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("数据-统计列表")]
    public async Task<SqlSugarPagedList<PageBasicDataListOutput>> PageDataList(PageDataListInput input)
    {
        return await _advertisementService.PageBasicDataList(input,true);
    }
}
