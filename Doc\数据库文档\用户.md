
# 用户相关数据库文档

[toc]

## 用户信息表 (UserInfo)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|  
|1|Id|bigint|Id|
|2|Phone|nvarchar(11)|手机号码|
|3|Password|nvarchar(512)|密码|
|4|RealName|nvarchar(32)|真实姓名|
|5|NickName|nvarchar(32)|昵称|
|6|Avatar|nvarchar(512)|头像|
|7|Sex|int|性别 1男 2女|
|9|Birthday|datetime|出生日期|
|14|ProvinceCode|int|省code，例110000|
|14|Province|nvarchar(64)|省，例北京市|
|14|CityCode|int|城市code，例110100|
|14|City|nvarchar(64)|城市，例北京市|
|14|CountyCode|int|区县code，例如110101|
|14|County|nvarchar(64)|区县，例如东城区|
|17|Introduction|nvarchar(512)|个人简介|
|18|Status|int|状态|
|19|Remark|nvarchar(256)|备注|
|20|LastLoginIp|nvarchar(256)|最新登录Ip|
|21|LastLoginAddress|nvarchar(128)|最新登录地点|
|22|LastLoginTime|datetime|最新登录时间|
|23|LastLoginDevice|nvarchar(128)|最新登录设备|
|24|TotalCoin|decimal(18,2)|累计诺币数量|
|25|OverCoin|decimal(18,2)|剩余诺币数量|
|26|CreateTime|datetime|创建时间|
|27|UpdateTime|datetime|更新时间|
|28|CreateUserId|bigint|创建者Id|
|29|UpdateUserId|bigint|修改者Id|
|30|IsDelete|bit|软删除|
|31|SourceType|int|数据来源类型 1后台 2 H5|
|32|**IsCreator**|bit|是否是创作者 0 否 1是|
|26|**CreatorTime**|datetime|成为创作者时间|
|32|**Zodiac**|string|星座，字典code值|

## ~~用户统计表UserStatistics~~

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|主键Id|
|UserId|bigint|UserId|
|Fans|int|粉丝量|
|Follow|int|关注量|
|Publish|int|发布量|

## 用户诺币明细表 (UserCoinDetail)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|UserId|bigint|UserId|
|3|Coin|decimal(18,2)|诺币|
|4|CoinType|int|诺币明细类型|
|5|Type|int|诺币记录类型 收入 or 支出|
|6|Remark|nvarchar(256)|备注|
|7|CreateTime|datetime|创建时间|  
|8|UpdateTime|datetime|更新时间|
|9|CreateUserId|bigint|创建者Id|
|10|UpdateUserId|bigint|修改者Id|
|11|IsDelete|bit|软删除|

## 用户内容收藏表 (UserCollect)

`取消收藏物理删除数据` 
| 列名 | 类型 | 说明 |
|-|-|-|  
|Id|bigint|Id|
|UserId|bigint|用户信息表主键id|
|CircleId|bigint|圈子内容表主键id|
|CreateTime|datetime|创建时间| 

## 用户浏览历史表 (UserBrowseHistory)

| 列名 | 类型 | 说明 |
|-|-|-|  
|Id|bigint|Id|
|UserId|bigint|用户信息表主键id|
|CircleId|bigint|圈子内容表主键id|
|CreateDate|datetime|创建日期，格式‘yyyy-MM-dd’|  
|CreateTime|datetime|创建时间|  
