namespace HRCommunity.NET.Core;

/// <summary>
/// 运营后台伪登录Token表
/// </summary>
[SugarTable(null, "运营后台伪登录Token表")]
[SystemTable]
public class SysImpersonateToken : EntityBase
{
    /// <summary>
    /// 伪登录Token（唯一）
    /// </summary>
    [SugarColumn(ColumnDescription = "伪登录Token", Length = 64, IsNullable = false)]
    [Required, MaxLength(64)]
    public string Token { get; set; }

    /// <summary>
    /// 目标用户ID（客户端用户）
    /// </summary>
    [SugarColumn(ColumnDescription = "目标用户ID")]
    public long UserId { get; set; }

    /// <summary>
    /// 创建Token的管理员ID
    /// </summary>
    [SugarColumn(ColumnDescription = "创建Token的管理员ID")]
    public long AdminId { get; set; }

    /// <summary>
    /// 是否已使用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否已使用")]
    public bool IsUsed { get; set; } = false;

    /// <summary>
    /// Token过期时间
    /// </summary>
    [SugarColumn(ColumnDescription = "Token过期时间")]
    public DateTime ExpireTime { get; set; }

    /// <summary>
    /// Token使用时间
    /// </summary>
    [SugarColumn(ColumnDescription = "Token使用时间")]
    public DateTime? UseTime { get; set; }

    /// <summary>
    /// 使用Token的IP地址
    /// </summary>
    [SugarColumn(ColumnDescription = "使用Token的IP地址", Length = 64)]
    [MaxLength(64)]
    public string? UseIp { get; set; }

    /// <summary>
    /// 使用Token的设备信息
    /// </summary>
    [SugarColumn(ColumnDescription = "使用Token的设备信息", Length = 128)]
    [MaxLength(128)]
    public string? UseDevice { get; set; }

    /// <summary>
    /// 备注说明
    /// </summary>
    [SugarColumn(ColumnDescription = "备注说明", Length = 256)]
    [MaxLength(256)]
    public string? Remark { get; set; }

    /// <summary>
    /// 目标用户信息
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(UserId))]
    public UserInfo User { get; set; }

    /// <summary>
    /// 创建Token的管理员信息
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    [Navigate(NavigateType.OneToOne, nameof(AdminId))]
    public SysAdmin Admin { get; set; }
}
