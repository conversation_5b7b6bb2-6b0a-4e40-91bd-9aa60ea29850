
# 系统通用数据库设计文档 

[toc]

## 系统配置表(SysConfig)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Name|nvarchar(64)|名称|
|3|Code|nvarchar(64)|编码|
|4|Value|nvarchar(64)|属性值|
|5|SysFlag|int|是否是内置参数|  
|6|GroupCode|nvarchar(64)|分组编码|
|7|OrderNo|int|排序|
|8|Remark|nvarchar(256)|备注|
|9|CreateTime|datetime|创建时间|
|10|UpdateTime|datetime|更新时间|
|11|CreateUserId|bigint|创建者Id|
|12|UpdateUserId|bigint|修改者Id|
|13|IsDelete|bit|软删除|

## 系统字典值表(SysDictData)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|DictTypeId|bigint|字典类型Id|
|3|Value|nvarchar(128)|值|
|4|Code|nvarchar(64)|编码|
|5|OrderNo|int|排序|
|6|Remark|nvarchar(128)|备注|  
|7|Status|int|状态|
|8|CreateTime|datetime|创建时间|
|9|UpdateTime|datetime|更新时间|
|10|CreateUserId|bigint|创建者Id|
|11|UpdateUserId|bigint|修改者Id|
|12|IsDelete|bit|软删除|

## 系统字典类型表(SysDictType)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Name|nvarchar(64)|名称|
|3|Code|nvarchar(64)|编码|
|4|OrderNo|int|排序|
|5|Remark|nvarchar(256)|备注|
|6|Status|int|状态|
|7|Flag|int|是否是内置参数|
|8|CreateTime|datetime|创建时间|
|9|UpdateTime|datetime|更新时间|
|10|CreateUserId|bigint|创建者Id|  
|11|UpdateUserId|bigint|修改者Id|
|12|IsDelete|bit|软删除|

## 系统文件表(SysFile)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Provider|nvarchar(128)|提供者|
|3|BucketName|nvarchar(128)|仓储名称|
|4|FileName|nvarchar(128)|文件名称|
|5|Suffix|nvarchar(16)|文件后缀|
|6|FilePath|nvarchar(128)|存储路径|
|7|SizeKb|nvarchar(16)|文件大小KB|
|8|SizeInfo|nvarchar(64)|文件大小信息|
|9|Url|nvarchar(128)|外链地址|
|10|CreateTime|datetime|创建时间|
|11|UpdateTime|datetime|更新时间|
|12|CreateUserId|bigint|创建者Id|
|13|UpdateUserId|bigint|修改者Id|
|14|IsDelete|bit|软删除|

## ~~系统作业集群表(SysJobCluster)~~

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|ClusterId|nvarchar(64)|作业集群Id|
|3|Description|nvarchar(128)|描述信息|
|4|Status|int|状态|
|5|UpdatedTime|datetime|更新时间|

## ~~系统作业信息表(SysJobDetail)~~

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|JobId|nvarchar(64)|作业Id|
|3|GroupName|nvarchar(128)|组名称|  
|4|JobType|nvarchar(128)|作业类型|
|5|AssemblyName|nvarchar(128)|程序集|
|6|Description|nvarchar(128)|描述信息|
|7|Concurrent|bit|是否并行执行|
|8|annotation|bit|是否扫描特性触发器|
|9|Properties|nvarchar(max)|额外数据|
|10|UpdatedTime|datetime|更新时间|
|11|CreateType|int|作业创建类型|
|12|ScriptCode|nvarchar(max)|脚本代码|

## ~~系统作业触发器表(SysJobTrigger)~~

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|TriggerId|nvarchar(64)|触发器Id|
|3|JobId|nvarchar(64)|作业Id|
|4|TriggerType|nvarchar(128)|触发器类型|  
|5|AssemblyName|nvarchar(128)|程序集|
|6|Args|nvarchar(128)|参数|
|7|Description|nvarchar(128)|描述信息|  
|8|Status|int|状态|
|9|StartTime|datetime|起始时间|
|10|EndTime|datetime|结束时间|
|11|LastRunTime|datetime|最近运行时间|
|12|NextRunTime|datetime|下一次运行时间|
|13|NumberOfRuns|bigint|触发次数|
|14|MaxNumberOfRuns|bigint|最大触发次数|
|15|NumberOfErrors|bigint|出错次数|
|16|MaxNumberOfErrors|bigint|最大出错次数|  
|17|NumRetries|int|重试次数|
|18|RetryTimeout|int|重试间隔时间(ms)|
|19|StartNow|bit|是否立即启动|
|20|RunOnStart|bit|是否启动时执行一次|
|21|ResetOnlyOnce|bit|是否重置触发次数| 
|22|UpdatedTime|datetime|更新时间|

## 系统审计日志表(SysLogAudit)  

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|TableName|nvarchar(64)|表名|
|3|ColumnName|nvarchar(64)|列名|
|4|NewValue|nvarchar(max)|新值|
|5|OldValue|nvarchar(max)|旧值|
|6|Operate|int|操作方式|
|7|AuditTime|datetime|审计时间|
|8|Account|nvarchar(32)|账号|
|9|RealName|nvarchar(32)|真实姓名|  
|10|CreateTime|datetime|创建时间|
|11|UpdateTime|datetime|更新时间|
|12|CreateUserId|bigint|创建者Id|
|13|UpdateUserId|bigint|修改者Id|
|14|IsDelete|bit|软删除|

## 系统差异日志表(SysLogDiff)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|  
|2|BeforeData|nvarchar(max)|操作前记录|
|3|AfterData|nvarchar(max)|操作后记录|
|4|Sql|nvarchar(max)|Sql|
|5|Parameters|nvarchar(max)|参数|
|6|BusinessData|nvarchar(max)|业务对象|
|7|DiffType|nvarchar(max)|差异操作|
|8|Elapsed|bigint|耗时|
|9|CreateTime|datetime|创建时间|
|10|UpdateTime|datetime|更新时间|
|11|CreateUserId|bigint|创建者Id|
|12|UpdateUserId|bigint|修改者Id|
|13|IsDelete|bit|软删除|

## 系统异常日志表(SysLogEx)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|HttpMethod|nvarchar(32)|请求方式|
|3|RequestUrl|nvarchar(max)|请求地址|  
|4|RequestParam|nvarchar(max)|请求参数|
|5|ReturnResult|nvarchar(max)|返回结果|
|6|EventId|int|事件Id|
|7|ThreadId|int|线程Id|
|8|TraceId|nvarchar(128)|请求跟踪Id|
|9|Exception|nvarchar(max)|异常信息|
|10|Message|nvarchar(max)|日志消息Json|
|11|ControllerName|nvarchar(256)|模块名称|
|12|ActionName|nvarchar(256)|方法名称|
|13|DisplayTitle|nvarchar(256)|显示名称|
|14|Status|nvarchar(32)|执行状态|  
|15|RemoteIp|nvarchar(256)|IP地址| 
|16|Location|nvarchar(128)|登录地点|
|17|Longitude|float|经度|
|18|Latitude|float|维度|
|19|Browser|nvarchar(1024)|浏览器|
|20|Os|nvarchar(256)|操作系统|
|21|Elapsed|bigint|操作用时|
|22|LogDateTime|datetime|日志时间|
|23|LogLevel|int|日志级别|
|24|Account|nvarchar(32)|账号| 
|25|RealName|nvarchar(32)|真实姓名|
|26|CreateTime|datetime|创建时间|
|27|UpdateTime|datetime|更新时间|
|28|CreateUserId|bigint|创建者Id|
|29|UpdateUserId|bigint|修改者Id|
|30|IsDelete|bit|软删除|

## 系统操作日志表(SysLogOp)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|HttpMethod|nvarchar(32)|请求方式|
|3|RequestUrl|nvarchar(max)|请求地址|
|4|RequestParam|nvarchar(max)|请求参数|  
|5|ReturnResult|nvarchar(max)|返回结果|
|6|EventId|int|事件Id|
|7|ThreadId|int|线程Id|
|8|TraceId|nvarchar(128)|请求跟踪Id|
|9|Exception|nvarchar(max)|异常信息|
|10|Message|nvarchar(max)|日志消息Json|
|11|ControllerName|nvarchar(256)|模块名称|
|12|ActionName|nvarchar(256)|方法名称|
|13|DisplayTitle|nvarchar(256)|显示名称|
|14|Status|nvarchar(32)|执行状态|
|15|RemoteIp|nvarchar(256)|IP地址|  
|16|Location|nvarchar(128)|登录地点|
|17|Longitude|float|经度|
|18|Latitude|float|维度|
|19|Browser|nvarchar(1024)|浏览器|
|20|Os|nvarchar(256)|操作系统|
|21|Elapsed|bigint|操作用时|
|22|LogDateTime|datetime|日志时间|
|23|LogLevel|int|日志级别|
|24|Account|nvarchar(32)|账号|
|25|RealName|nvarchar(32)|真实姓名|  
|26|CreateTime|datetime|创建时间|
|27|UpdateTime|datetime|更新时间|
|28|CreateUserId|bigint|创建者Id|
|29|UpdateUserId|bigint|修改者Id|
|30|IsDelete|bit|软删除|

## 系统访问日志表(SysLogVis)

| 序号 | 列名 | 类型 | 说明 | 
|-|-|-|-|
|1|Id|bigint|Id|
|2|ControllerName|nvarchar(256)|模块名称|
|3|ActionName|nvarchar(256)|方法名称|
|4|DisplayTitle|nvarchar(256)|显示名称|
|5|Status|nvarchar(32)|执行状态|
|6|RemoteIp|nvarchar(256)|IP地址|
|7|Location|nvarchar(128)|登录地点|
|8|Longitude|float|经度|
|9|Latitude|float|维度|   
|10|Browser|nvarchar(1024)|浏览器|
|11|Os|nvarchar(256)|操作系统|
|12|Elapsed|bigint|操作用时|
|13|LogDateTime|datetime|日志时间|
|14|LogLevel|int|日志级别|
|15|Account|nvarchar(32)|账号|
|16|RealName|nvarchar(32)|真实姓名|
|17|CreateTime|datetime|创建时间|
|18|UpdateTime|datetime|更新时间|
|19|CreateUserId|bigint|创建者Id|
|20|UpdateUserId|bigint|修改者Id|
|21|IsDelete|bit|软删除|

## 系统通知公告表(SysNotice) 

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Title|nvarchar(32)|标题|
|3|Content|nvarchar(max)|内容|  
|4|Type|int|类型(1通知 2公告)|
|5|PublicUserId|bigint|发布人Id|
|6|PublicUserName|nvarchar(32)|发布人姓名|
|7|PublicOrgId|bigint|发布机构Id|
|8|PublicOrgName|nvarchar(64)|发布机构名称|
|9|PublicTime|datetime|发布时间|
|10|CancelTime|datetime|撤回时间|  
|11|Status|int|状态(0草稿 1发布 2撤回 3删除)| 
|12|CreateTime|datetime|创建时间|
|13|UpdateTime|datetime|更新时间|
|14|CreateUserId|bigint|创建者Id|
|15|UpdateUserId|bigint|修改者Id|
|16|IsDelete|bit|软删除|

## 系统通知公告用户表(SysNoticeUser)

| 列名 | 类型 | 说明 |
|-|-|-|
|NoticeId|bigint|通知公告Id|
|UserId|bigint|用户Id|
|ReadTime|datetime|阅读时间|
|ReadStatus|int|状态(0未读 1已读)|

## 系统在线用户表(SysOnlineUser)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|  
|2|ConnectionId|nvarchar(255)|连接Id|
|3|UserId|bigint|用户Id|
|4|UserName|nvarchar(32)|账号|
|5|RealName|nvarchar(32)|真实姓名|
|6|Time|datetime|连接时间|
|7|Ip|nvarchar(256)|连接IP|
|8|Browser|nvarchar(128)|浏览器|
|9|Os|nvarchar(128)|操作系统|

## 系统行政地区表(SysRegion)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Pid|bigint|父Id|
|3|Name|nvarchar(64)|名称|  
|4|ShortName|nvarchar(32)|简称|
|5|MergerName|nvarchar(64)|组合名|
|6|Code|nvarchar(32)|行政代码|
|7|ZipCode|nvarchar(6)|邮政编码|
|8|CityCode|nvarchar(6)|区号|
|9|Level|int|层级|
|10|PinYin|nvarchar(128)|拼音|  
|11|Lng|float|经度|
|12|Lat|float|维度|
|13|OrderNo|int|排序|
|14|Remark|nvarchar(128)|备注|

## 系统微信支付表(SysWechatPay)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|MerchantId|nvarchar(255)|微信商户号|  
|3|AppId|nvarchar(255)|服务商AppId|
|4|OutTradeNumber|nvarchar(255)|商户订单号|
|5|TransactionId|nvarchar(255)|支付订单号|
|6|TradeType|nv