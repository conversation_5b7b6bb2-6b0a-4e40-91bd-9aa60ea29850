﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HRCommunity.NET.Application;
public class ImportCourseDto
{
}


/// <summary>
/// 批量导入结果导出
/// </summary>
public class CertCourseResultImportOutput
{
    public bool IsSuccess { get; set; }
    public string ErrorMessage { get; set; }
    public List<CertCourseBatchImportOutput> Details { get; set; }
}
public class CertCourseBatchImportOutput : ImportCertCourseUserDataDto
{
    /// <summary>
    /// 是否校验成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> ErrorMessage { get; set; }

    /// <summary>
    /// 用户Id
    /// </summary>
    [JsonIgnore]
    public long UserId { get; set; }

    /// <summary>
    /// 是否新用户
    /// </summary>
    [JsonIgnore]
    public bool IsNew { get; set; }

    /// <summary>
    /// 订单Id
    /// </summary>
    [JsonIgnore]
    public long OrderId { get; set; }
}

public class ImportCertCourseUserDataDto
{
    /// <summary>
    /// 姓名
    /// </summary>
    [ImporterHeader(Name = "姓名",ColumnIndex = 1)]
    public string Name { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    [ImporterHeader(Name = "手机号", ColumnIndex = 2)]
    public string Phone { get; set; }

    /// <summary>
    /// 推荐人
    /// </summary>
    [ImporterHeader(Name = "推荐人")]
    public string Referrer { get; set; }

    /// <summary>
    /// 公司名称
    /// </summary>
    [ImporterHeader(Name = "公司名称")]
    public string Company { get; set; }

    /// <summary>
    /// 所属行业
    /// </summary>
    [ImporterHeader(Name = "所属行业")]
    public string Industry { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    [ImporterHeader(Name = "职位")]
    public string Position { get; set; }
}