using Microsoft.AspNetCore.Authorization;

namespace HRCommunity.NET.Web.Core;

/// <summary>
/// 客户端伪登录服务
/// </summary>
[ApiDescriptionSettings("01.H5登录", Order = 515)]
[Route("api/[controller]")]
public class SysImpersonateLoginController : IDynamicApiController
{
    private readonly ISysImpersonateTokenService _impersonateTokenService;
    private readonly ISysUserAuthService _authService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SysImpersonateLoginController(
        ISysImpersonateTokenService impersonateTokenService,
        ISysUserAuthService authService,
        IHttpContextAccessor httpContextAccessor)
    {
        _impersonateTokenService = impersonateTokenService;
        _authService = authService;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// 伪登录接口
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("login")]
    [AllowAnonymous]
    [DisplayName("伪登录接口")]
    public async Task<LoginOutput> ImpersonateLogin(ImpersonateLoginInput input)
    {
        // 获取客户端信息
        var httpContext = _httpContextAccessor.HttpContext;
        var clientIp = GetClientIP(httpContext);
        var userAgent = httpContext.Request.Headers["User-Agent"].ToString();

        // 验证并使用Token
        var targetUser = await _impersonateTokenService.ValidateAndUseTokenAsync(input.Token, clientIp, userAgent);

        // 使用现有的登录逻辑生成JWT Token
        var loginResult = await GenerateLoginToken(targetUser);

        return loginResult;
    }

    /// <summary>
    /// 生成登录Token（复用现有登录逻辑）
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    private async Task<LoginOutput> GenerateLoginToken(UserInfo user)
    {
        // 这里复用现有的DoLogin逻辑
        // 由于DoLogin是私有方法，我们需要重新实现相同的逻辑
        
        // 单用户登录处理
        var sysOnlineUserService = App.GetService<ISysOnlineUserService>();
        await sysOnlineUserService.SignleLogin(user.Id);

        // 记录登录日志
        await AddUserLoginLog(user.Id);

        // 获取Token配置
        var sysConfigService = App.GetService<ISysConfigService>();
        var tokenExpire = await sysConfigService.GetTokenExpire();
        var refreshTokenExpire = await sysConfigService.GetRefreshTokenExpire();

        // 生成Token令牌
        var accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            { ClaimConst.UserId, user.Id },
            { ClaimConst.AccountType, AccountTypeEnum.User },
            { ClaimConst.LoginMode, LoginModeEnum.APP },
            { ClaimConst.OpenId, "" }
        }, tokenExpire);

        // 生成刷新Token令牌
        var refreshToken = JWTEncryption.GenerateRefreshToken(accessToken, refreshTokenExpire);

        // 设置响应报文头
        SetTokensOfResponseHeaders(_httpContextAccessor.HttpContext, accessToken, refreshToken);

        return new LoginOutput
        {
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            Expire = tokenExpire,
            ExpireTime = DateTime.Now.AddMinutes(tokenExpire - 1)
        };
    }

    /// <summary>
    /// 添加用户登录日志
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    private async Task AddUserLoginLog(long userId)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var clientIp = GetClientIP(httpContext);
        var userAgent = httpContext.Request.Headers["User-Agent"].ToString();
        
        var userInfoRep = App.GetService<SqlSugarRepository<UserInfo>>();
        await userInfoRep.AsUpdateable()
            .SetColumns(u => new UserInfo
            {
                LastLoginTime = DateTime.Now,
                LastLoginIp = clientIp,
                LastLoginAddress = "未知", // 简化处理，不解析IP地址
                LastLoginDevice = userAgent
            })
            .Where(u => u.Id == userId)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取客户端IP地址
    /// </summary>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    private string GetClientIP(HttpContext httpContext)
    {
        var ip = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (string.IsNullOrEmpty(ip))
        {
            ip = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
        }
        if (string.IsNullOrEmpty(ip))
        {
            ip = httpContext.Connection.RemoteIpAddress?.ToString();
        }
        return ip ?? "127.0.0.1";
    }

    /// <summary>
    /// 设置响应头Token
    /// </summary>
    /// <param name="httpContext"></param>
    /// <param name="accessToken"></param>
    /// <param name="refreshToken"></param>
    private void SetTokensOfResponseHeaders(HttpContext httpContext, string accessToken, string refreshToken)
    {
        httpContext.Response.Headers["access-token"] = accessToken;
        httpContext.Response.Headers["x-access-token"] = refreshToken;
    }
}
