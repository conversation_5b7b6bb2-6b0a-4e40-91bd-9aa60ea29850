﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 圈子-分组服务
/// </summary>
[ApiDescriptionSettings("Circle", Order = 480)]
public class CircleGroupController : IDynamicApiController
{
    private readonly ICircleGroupService _circleGroupService;
    public CircleGroupController(ICircleGroupService circleGroupService)
    {
        _circleGroupService = circleGroupService;
    }
    /// <summary>
    /// 获取圈子类型下拉列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取圈子类型下拉列表")]
    public async Task<List<CircleGroupDropDownOutput>> GetDropDownList()
    {
        return await _circleGroupService.GetDropDownList();
    }

    /// <summary>
    /// 获取圈子类型分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取圈子类型分页列表")]
    public async Task<SqlSugarPagedList<CircleGroup>> Page(PageCircleGroupInput input)
    {
        return await _circleGroupService.Page(input);
    }

    /// <summary>
    /// 增加分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加分组")]
    public async Task<bool> AddCate(AddCircleGroupInput input)
    {
        return await _circleGroupService.Add(input);
    }

    /// <summary>
    /// 更新分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新分组")]
    public async Task<bool> UpdateCate(UpdateCircleGroupInput input)
    {
        return await _circleGroupService.Update(input);
    }


    /// <summary>
    /// 删除分组
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除分组")]
    public async Task<bool> DeleteCate(BaseIdInput input)
    {
        return await _circleGroupService.Delete(input);
    }

    /// <summary>
    /// 设置分组状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置分组状态")]
    public async Task<bool> SetStatus(CircleGroupInput input)
    {
        return await _circleGroupService.SetStatus(input);
    }
}
