﻿
namespace HRCommunity.NET.Application;
public class <PERSON>ShortHelper
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ISysCacheService _sysCacheService;
    private readonly NoahShortConfig _config;

    public NoahShortHelper(IHttpClientFactory httpClientFactory,
                        ISysCacheService sysCacheService,
                        IOptions<NoahShortConfig> Options)
    {
        _httpClientFactory = httpClientFactory;
        _sysCacheService = sysCacheService;
        _config = Options.Value;
    }

    /// <summary>
    /// 获取短链接
    /// </summary>
    /// <returns></returns>
    public async Task<string> GetShortUrl(string longUrl)
    {
        var url = string.Empty;
        var input = new
        {
            longURL = longUrl,
            secretkey = _config.Secret
        };
        var response = await Post("/generate", input);
        Console.WriteLine(response);
        var obj = JSON.Deserialize<INoahBaseOutput<string>>(response);
        if (obj.Code == 200)
        {
            url = obj.Data;
        }
        return url;
    }

    /// <summary>
    /// API POST请求方法
    /// </summary>
    /// <param name="method">方法名称</param>
    /// <param name="param">请求参数</param>
    /// <returns></returns>
    private async Task<string> Post(string method, object param)
    {
        string ret = string.Empty;
        using (var client = _httpClientFactory.CreateClient())
        {
            // 设置要请求的 URL  
            string url = $"{_config.ApiUrl}{method}";
            //url = $"https://openapi.nuopin.cn/{method}";

            // 将数据序列化为 JSON 格式  
            string content = JSON.Serialize(param);

            // 创建 HTTP POST 请求的内容  
            HttpContent httpContent = new StringContent(content, System.Text.Encoding.UTF8, "application/json");

            //if (!method.Contains("Token"))
            //{
            //    var token = await GetToken();
            //    client.DefaultRequestHeaders.Add("Authorization", "Bearer " + token);
            //}

            // 发送 POST 请求并获取响应  
            HttpResponseMessage response = await client.PostAsync(url, httpContent);

            // 检查响应是否成功  
            if (response.IsSuccessStatusCode)
            {
                // 读取响应内容  
                ret = await response.Content.ReadAsStringAsync();

                // 打印响应内容  
                Console.WriteLine(ret);
            }
            else
            {
                Console.WriteLine($"请求失败，状态码：{response.StatusCode}");
            }
        }
        return ret;
    }

}
