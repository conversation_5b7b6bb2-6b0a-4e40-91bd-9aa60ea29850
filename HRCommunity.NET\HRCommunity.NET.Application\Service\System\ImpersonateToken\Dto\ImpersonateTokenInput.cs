namespace HRCommunity.NET.Application;

/// <summary>
/// 生成伪登录Token输入参数
/// </summary>
public class GenerateImpersonateTokenInput
{
    /// <summary>
    /// 目标用户ID
    /// </summary>
    [Required(ErrorMessage = "目标用户ID不能为空")]
    public long UserId { get; set; }

    /// <summary>
    /// Token有效期（分钟）
    /// </summary>
    [Range(1, 1440, ErrorMessage = "Token有效期必须在1-1440分钟之间")]
    public int ExpireMinutes { get; set; } = 30;

    /// <summary>
    /// 备注说明
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注说明不能超过256个字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 伪登录Token输出
/// </summary>
public class ImpersonateTokenOutput
{
    /// <summary>
    /// Token ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 伪登录Token
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    /// 目标用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 目标用户手机号
    /// </summary>
    public string UserPhone { get; set; }

    /// <summary>
    /// 目标用户昵称
    /// </summary>
    public string UserNickName { get; set; }

    /// <summary>
    /// 创建Token的管理员ID
    /// </summary>
    public long AdminId { get; set; }

    /// <summary>
    /// 创建Token的管理员账号
    /// </summary>
    public string AdminAccount { get; set; }

    /// <summary>
    /// 是否已使用
    /// </summary>
    public bool IsUsed { get; set; }

    /// <summary>
    /// Token过期时间
    /// </summary>
    public DateTime ExpireTime { get; set; }

    /// <summary>
    /// Token使用时间
    /// </summary>
    public DateTime? UseTime { get; set; }

    /// <summary>
    /// 使用Token的IP地址
    /// </summary>
    public string? UseIp { get; set; }

    /// <summary>
    /// 使用Token的设备信息
    /// </summary>
    public string? UseDevice { get; set; }

    /// <summary>
    /// 备注说明
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
}

/// <summary>
/// 伪登录Token分页查询输入
/// </summary>
public class ImpersonateTokenPageInput : BasePageInput
{
    /// <summary>
    /// 目标用户ID
    /// </summary>
    public long? UserId { get; set; }

    /// <summary>
    /// 目标用户手机号
    /// </summary>
    public string? UserPhone { get; set; }

    /// <summary>
    /// 创建Token的管理员ID
    /// </summary>
    public long? AdminId { get; set; }

    /// <summary>
    /// 是否已使用
    /// </summary>
    public bool? IsUsed { get; set; }

    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool? IsExpired { get; set; }
}

/// <summary>
/// 客户端伪登录输入参数
/// </summary>
public class ImpersonateLoginInput
{
    /// <summary>
    /// 伪登录Token
    /// </summary>
    [Required(ErrorMessage = "伪登录Token不能为空")]
    [MaxLength(64, ErrorMessage = "Token格式不正确")]
    public string Token { get; set; }
}
