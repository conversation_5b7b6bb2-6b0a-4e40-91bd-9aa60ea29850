using HRCommunity.NET.Core;
using HRCommunity.NET.SqlSugar;

namespace HRCommunity.NET.Application;

/// <summary>
/// 运营后台伪登录Token服务实现
/// </summary>
public class SysImpersonateTokenService : ISysImpersonateTokenService
{
    private readonly SqlSugarRepository<SysImpersonateToken> _impersonateTokenRep;
    private readonly SqlSugarRepository<UserInfo> _userInfoRep;
    private readonly SqlSugarRepository<SysAdmin> _sysAdminRep;
    private readonly UserManager _userManager;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public SysImpersonateTokenService(
        SqlSugarRepository<SysImpersonateToken> impersonateTokenRep,
        SqlSugarRepository<UserInfo> userInfoRep,
        SqlSugarRepository<SysAdmin> sysAdminRep,
        UserManager userManager,
        IHttpContextAccessor httpContextAccessor)
    {
        _impersonateTokenRep = impersonateTokenRep;
        _userInfoRep = userInfoRep;
        _sysAdminRep = sysAdminRep;
        _userManager = userManager;
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// 生成伪登录Token
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<ImpersonateTokenOutput> GenerateTokenAsync(GenerateImpersonateTokenInput input)
    {
        // 验证目标用户是否存在
        var targetUser = await _userInfoRep.GetByIdAsync(input.UserId);
        if (targetUser == null)
            throw Oops.Oh("目标用户不存在");

        // 获取当前管理员信息
        var currentAdminId = _userManager.UserId;
        var currentAdmin = await _sysAdminRep.GetByIdAsync(currentAdminId);
        if (currentAdmin == null)
            throw Oops.Oh("当前管理员信息不存在");

        // 生成唯一Token
        var token = GenerateUniqueToken();

        // 创建伪登录Token记录
        var impersonateToken = new SysImpersonateToken
        {
            Token = token,
            UserId = input.UserId,
            AdminId = currentAdminId,
            IsUsed = false,
            ExpireTime = DateTime.Now.AddMinutes(input.ExpireMinutes),
            Remark = input.Remark
        };

        var result = await _impersonateTokenRep.InsertReturnEntityAsync(impersonateToken);

        return new ImpersonateTokenOutput
        {
            Id = result.Id,
            Token = result.Token,
            UserId = result.UserId,
            UserPhone = targetUser.Phone,
            UserNickName = targetUser.NickName,
            AdminId = result.AdminId,
            AdminAccount = currentAdmin.Account,
            IsUsed = result.IsUsed,
            ExpireTime = result.ExpireTime,
            UseTime = result.UseTime,
            UseIp = result.UseIp,
            UseDevice = result.UseDevice,
            Remark = result.Remark,
            CreateTime = result.CreateTime
        };
    }

    /// <summary>
    /// 分页查询伪登录Token
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<ImpersonateTokenOutput>> GetPageAsync(ImpersonateTokenPageInput input)
    {
        var query = _impersonateTokenRep.AsQueryable()
            .LeftJoin<UserInfo>((t, u) => t.UserId == u.Id)
            .LeftJoin<SysAdmin>((t, u, a) => t.AdminId == a.Id)
            .WhereIF(input.UserId.HasValue, (t, u, a) => t.UserId == input.UserId.Value)
            .WhereIF(!string.IsNullOrWhiteSpace(input.UserPhone), (t, u, a) => u.Phone.Contains(input.UserPhone))
            .WhereIF(input.AdminId.HasValue, (t, u, a) => t.AdminId == input.AdminId.Value)
            .WhereIF(input.IsUsed.HasValue, (t, u, a) => t.IsUsed == input.IsUsed.Value);

        // 处理过期状态筛选
        if (input.IsExpired.HasValue)
        {
            if (input.IsExpired.Value)
                query = query.Where((t, u, a) => t.ExpireTime < DateTime.Now);
            else
                query = query.Where((t, u, a) => t.ExpireTime >= DateTime.Now);
        }

        var result = await query
            .OrderByDescending((t, u, a) => t.CreateTime)
            .Select((t, u, a) => new ImpersonateTokenOutput
            {
                Id = t.Id,
                Token = t.Token,
                UserId = t.UserId,
                UserPhone = u.Phone,
                UserNickName = u.NickName,
                AdminId = t.AdminId,
                AdminAccount = a.Account,
                IsUsed = t.IsUsed,
                ExpireTime = t.ExpireTime,
                UseTime = t.UseTime,
                UseIp = t.UseIp,
                UseDevice = t.UseDevice,
                Remark = t.Remark,
                CreateTime = t.CreateTime
            })
            .ToPagedListAsync(input.Page, input.PageSize);

        return result;
    }

    /// <summary>
    /// 获取伪登录Token详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<ImpersonateTokenOutput> GetDetailAsync(long id)
    {
        var result = await _impersonateTokenRep.AsQueryable()
            .LeftJoin<UserInfo>((t, u) => t.UserId == u.Id)
            .LeftJoin<SysAdmin>((t, u, a) => t.AdminId == a.Id)
            .Where((t, u, a) => t.Id == id)
            .Select((t, u, a) => new ImpersonateTokenOutput
            {
                Id = t.Id,
                Token = t.Token,
                UserId = t.UserId,
                UserPhone = u.Phone,
                UserNickName = u.NickName,
                AdminId = t.AdminId,
                AdminAccount = a.Account,
                IsUsed = t.IsUsed,
                ExpireTime = t.ExpireTime,
                UseTime = t.UseTime,
                UseIp = t.UseIp,
                UseDevice = t.UseDevice,
                Remark = t.Remark,
                CreateTime = t.CreateTime
            })
            .FirstAsync();

        if (result == null)
            throw Oops.Oh("伪登录Token不存在");

        return result;
    }

    /// <summary>
    /// 删除伪登录Token
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task DeleteAsync(long id)
    {
        var token = await _impersonateTokenRep.GetByIdAsync(id);
        if (token == null)
            throw Oops.Oh("伪登录Token不存在");

        await _impersonateTokenRep.DeleteAsync(token);
    }

    /// <summary>
    /// 验证并使用伪登录Token（客户端调用）
    /// </summary>
    /// <param name="token"></param>
    /// <param name="clientIp"></param>
    /// <param name="userAgent"></param>
    /// <returns></returns>
    public async Task<UserInfo> ValidateAndUseTokenAsync(string token, string clientIp, string userAgent)
    {
        // 查找Token记录
        var impersonateToken = await _impersonateTokenRep.AsQueryable()
            .Where(t => t.Token == token)
            .FirstAsync();

        if (impersonateToken == null)
            throw Oops.Oh("无效的伪登录Token");

        // 检查Token是否已使用
        if (impersonateToken.IsUsed)
            throw Oops.Oh("伪登录Token已被使用");

        // 检查Token是否过期
        if (impersonateToken.ExpireTime < DateTime.Now)
            throw Oops.Oh("伪登录Token已过期");

        // 获取目标用户信息
        var targetUser = await _userInfoRep.GetByIdAsync(impersonateToken.UserId);
        if (targetUser == null)
            throw Oops.Oh("目标用户不存在");

        // 标记Token为已使用
        impersonateToken.IsUsed = true;
        impersonateToken.UseTime = DateTime.Now;
        impersonateToken.UseIp = clientIp;
        impersonateToken.UseDevice = userAgent;

        await _impersonateTokenRep.UpdateAsync(impersonateToken);

        return targetUser;
    }

    /// <summary>
    /// 生成唯一Token
    /// </summary>
    /// <returns></returns>
    private string GenerateUniqueToken()
    {
        return Guid.NewGuid().ToString("N");
    }
}
