﻿using Furion.DatabaseAccessor;
using Furion.RemoteRequest;
using HRCommunity.NET.Application;
using HRCommunity.NET.Application.IService;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 线下课
/// </summary>
[ApiDescriptionSettings("线下课", Order = 400)]
public class OfflineCourseController : IDynamicApiController
{
    private readonly IOfflineCourseService _offlineCourseService;
    public OfflineCourseController(IOfflineCourseService offlineCourseService)
    {
        _offlineCourseService = offlineCourseService;
    }

    /// <summary>
    /// 获取线下课详情，没有创建
    /// 点击课程管理时就调取本接口
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [DisplayName("获取线下课详情")]
    public async Task<OfflineCourseDetailOutput> GetOrCreate(OfflineCourseDetailInput input)
    {
        return await _offlineCourseService.GetOrCreate(input);
    }

    /// <summary>
    /// 互动列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("互动列表")]
    public async Task<List<CourseInteractionOutput>> InteractionList(CourseInteractionInput input)
    {
        return await _offlineCourseService.InteractionList(input);
    }

    /// <summary>
    /// 修改互动状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("修改互动状态")]
    public async Task<bool> InteractionStatus(StatusInteractionInput input)
    {
        return await _offlineCourseService.InteractionStatus(input);
    }

    /// <summary>
    /// 删除互动信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除互动信息")]
    public async Task<bool> InteractionDelete(BaseIdInput input)
    {
        return await _offlineCourseService.InteractionDelete(input);
    }

    /// <summary>
    /// 互动信息排序
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("互动信息排序")]
    public async Task<bool> InteractionOrder(OrderInteractionInput input)
    {
        return await _offlineCourseService.InteractionOrder(input);
    }

    /// <summary>
    /// 新增签到信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("新增签到信息")]
    public async Task<bool> AddSignIn(AddSignInInput input)
    {
        return await _offlineCourseService.AddSignIn(input);
    }

    /// <summary>
    /// 编辑签到信息
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("编辑签到信息")]
    public async Task<bool> EditSignIn(EditSignInInput input)
    {
        return await _offlineCourseService.EditSignIn(input);
    }

    /// <summary>
    /// 获取签到详情
    /// </summary>
    /// <param name="signInId"></param>
    /// <returns></returns>
    [DisplayName("获取签到详情")]
    public async Task<SignInOutput> GetSignIn(long signInId)
    {
        return await _offlineCourseService.GetSignIn(signInId);
    }

    /// <summary>
    /// 获取学员签到列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取学员签到列表")]
    public async Task<SqlSugarPagedList<PageSignInUserOutput>> PageSignIn(PageSignInUserInput input)
    {
        return await _offlineCourseService.PageSignIn(input);
    }
    /// <summary>
    /// 导出学员签到记录
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportSignInUser"), NonUnify]
    [DisplayName("导出学员签到记录")]
    public async Task<IActionResult> ExportSignInUser(PageSignInUserInput input)
    {
        input.Page = 1;
        input.PageSize = 100000;
        var signIn = await _offlineCourseService.GetSignIn(input.Id) ?? throw Oops.Oh("签到不存在");
        var page = await _offlineCourseService.PageSignIn(input);
        var list = page.Items;
        //todo
        //throw Oops.Oh("接口还没开发完");
        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");
        var headerRow = sheet.CreateRow(0);
        headerRow.CreateCell(0).SetCellValue("序号");
        headerRow.CreateCell(1).SetCellValue("姓名");
        headerRow.CreateCell(2).SetCellValue("手机号");
        var i = 3;
        signIn.ControlList.Where(x=>x.Name!="姓名").ForEach(x =>
        {
            headerRow.CreateCell(i).SetCellValue(x.Name);
            i++;
        });
        headerRow.CreateCell(i).SetCellValue("签到时间");
        int rowIndex = 1;
        foreach (var item in list)
        {
            var row = sheet.CreateRow(rowIndex++);
            row.CreateCell(0).SetCellValue(rowIndex-1);
            row.CreateCell(1).SetCellValue(item.RealName);
            row.CreateCell(2).SetCellValue(item.Phone);
            var r = 3;
            if (item.ControlList != null)
            {
                item.ControlList.Where(x => x.Name != "姓名").ForEach(x =>
                {
                    row.CreateCell(r).SetCellValue(x.Value);
                    r++;
                });
            }
            row.CreateCell(r).SetCellValue(item.CreateTime.Value.ToString("yyyy.MM.dd HH:mm:ss"));
        }

        using var ms = new MemoryStream();
        workbook.Write(ms);
        var res = ms.ToArray();

        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"学员签到记录.xlsx" };
    }

    /// <summary>
    /// 新增/编辑信息采集
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("新增/编辑问卷")]
    public async Task<bool> SaveFormDes(SaveFormDesInput input)
    {
        return await _offlineCourseService.SaveFormDes(input);
    }

    /// <summary>
    /// 课程设置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程设置")]
    public async Task<bool> SetCourse(SetOfflineCourseInput input)
    {
        return await _offlineCourseService.SetCourse(input);
    }

    /// <summary>
    /// 学员管理列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("学员管理列表")]
    public async Task<SqlSugarPagedList<PageOfflineUserOutput>> UserList(PageOfflineUserInput input)
    {
        var list = await _offlineCourseService.GetUserList(input);
        if (input.InteractionId > 0 && input.Complate.HasValue)
        {
            list = list.Where(x=> x.InteractionUsers.Any(i=>i.Id == input.InteractionId && i.Complate == input.Complate)).ToList();
        }
        return list.ToPagedList(input.Page, input.PageSize);
    }
    /// <summary>
    /// 导出学员管理列表
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "ExportOfflineUser"), NonUnify]
    [DisplayName("导出学员管理列表")]
    public async Task<IActionResult> ExportOfflineUser(PageOfflineUserInput input)
    {
        var list = await _offlineCourseService.GetUserList(input);
        if (input.InteractionId > 0 && input.Complate.HasValue)
        {
            list = list.Where(x => x.InteractionUsers.Any(i => i.Id == input.InteractionId && i.Complate == input.Complate)).ToList();
        }
        var interactionList = await _offlineCourseService.InteractionList(new CourseInteractionInput
        {
            Id = input.Id
        });
        var dynamicColumn = new List<string>();

        IWorkbook workbook = new XSSFWorkbook();
        ISheet sheet = workbook.CreateSheet("Sheet1");
        var headerRow = sheet.CreateRow(0);
        headerRow.CreateCell(0).SetCellValue("序号");
        headerRow.CreateCell(1).SetCellValue("姓名");
        headerRow.CreateCell(2).SetCellValue("手机号");
        headerRow.CreateCell(3).SetCellValue("部门");
        headerRow.CreateCell(4).SetCellValue("岗位");
        var i = 5;
        interactionList.ForEach(x =>
        {
            headerRow.CreateCell(i).SetCellValue((i-2)+"."+x.Name);
            i++;
        });
        headerRow.CreateCell(i).SetCellValue("状态");
        int rowIndex = 1;
        foreach (var item in list)
        {
            var row = sheet.CreateRow(rowIndex++);
            row.CreateCell(0).SetCellValue(rowIndex - 1);
            row.CreateCell(1).SetCellValue(item.RealName);
            row.CreateCell(2).SetCellValue(item.Phone);
            row.CreateCell(3).SetCellValue(item.Department);
            row.CreateCell(4).SetCellValue(item.Position);
            var r = 5;
            if (item.InteractionUsers != null)
            {
                item.InteractionUsers.ForEach(x =>
                {
                    row.CreateCell(r).SetCellValue(x.Complate ? $"已完成{(x.Type == 2 ? $"({x.TotalScore})" : "")}" : "未参与");
                    r++;
                });
            }
            row.CreateCell(r).SetCellValue(item.Status == StatusEnum.Enable ? "启用" : "禁用");
        }

        using var ms = new MemoryStream();
        workbook.Write(ms);
        var res = ms.ToArray();

        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"学员管理列表.xlsx" };
    }

    /// <summary>
    /// 添加参训学员
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("添加参训学员")]
    public async Task<bool> AddOfflineCourseUser(AddOfflineCourseUserInput input)
    {
        return await _offlineCourseService.AddOfflineCourseUser(input);
    }


    /// <summary>
    /// 编辑参训学员
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("编辑参训学员")]
    public async Task<bool> UpdateOfflineCourseUser(UpdateOfflineCourseUserInput input)
    {
        return await _offlineCourseService.UpdateOfflineCourseUser(input);
    }
    /// <summary>
    /// 批量导入参训学员
    /// </summary>
    /// <param name="file"></param>
    /// <param name="offlineCourseId">线下课id</param>
    /// <returns></returns>
    [DisplayName("批量导入参训学员")]
    public async Task<OfflineUserResultExportOutput> UploadFile([Required] IFormFile file, [FromQuery] long offlineCourseId)
    {
        var ret = await _offlineCourseService.UploadFile(file, offlineCourseId);

        return ret;
    }

    /// <summary>
    /// 更新参训学员状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<bool> SetOfflineCourseUserStatus(OfflineCourseUserStatusInput input)
    {
        return await _offlineCourseService.SetOfflineCourseUserStatus(input);
    }
    /// <summary>
    /// 删除参训学员
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除参训学员")]
    public async Task<bool> DeleteOfflineCourseUser(BaseIdInput input)
    {
        return await _offlineCourseService.DeleteOfflineCourseUser(input);
    }

    /// <summary>
    /// 获取项目照片/视频
    /// </summary>
    /// <param name="offlineCourseId"></param>
    /// <returns></returns>
    [DisplayName("获取项目照片/视频")]
    public async Task<ProjectPhotosOutput> GetProjectPhotos(long offlineCourseId)
    {
        return await _offlineCourseService.GetProjectPhotos(offlineCourseId);
    }

    /// <summary>
    /// 保存项目照片/视频
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("保存项目照片/视频")]
    public async Task<bool> SaveProjectPhotos(SaveProjectPhotosInput input)
    {
        return await _offlineCourseService.SaveProjectPhotos(input);
    }
}
