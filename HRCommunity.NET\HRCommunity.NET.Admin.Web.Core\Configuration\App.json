{
    "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

    "Urls": "http://*:5567", // 配置默认端口
    // "https_port": 44325,

    "AllowedHosts": "*",

    "AppSettings": {
        "InjectSpecificationDocument": true // 生产环境是否开启Swagger
    },
    "DynamicApiControllerSettings": {
        //"DefaultRoutePrefix": "api", // 默认路由前缀
        "CamelCaseSeparator": "", // 骆驼(驼峰)/帕斯卡命名分隔符
        "LowercaseRoute": false, // 小写路由格式
        "AsLowerCamelCase": true // 启用小驼峰命名（首字母小写）
        //"KeepVerb": false // 保留动作谓词
        //"KeepName": true // 保留默认名称
    },
    "FriendlyExceptionSettings": {
        "DefaultErrorMessage": "系统异常，请联系管理员",
        "ThrowBah": true, // 是否将 Oops.Oh 默认抛出为业务异常
        "LogError": false // 是否输出异常日志
    },
    "LocalizationSettings": {
        "SupportedCultures": [ "zh-CN", "en-US" ], // 语言列表
        "DefaultCulture": "zh-CN", // 默认语言
        "DateTimeFormatCulture": "zh-CN" // 固定时间区域为特定时区（多语言）
    },
    "CorsAccessorSettings": {
        "WithExposedHeaders": [ "Content-Disposition", "X-Pagination", "access-token", "x-access-token" ], // 如果前端不代理且是axios请求
        "SignalRSupport": true // 启用 SignalR 跨域支持
    },
    "SnowId": {
        "WorkerId": 1, // 机器码 全局唯一
        "WorkerIdBitLength": 1, // 机器码位长 默认值6，取值范围 [1, 19]
        "SeqBitLength": 6 // 序列数位长 默认值6，取值范围 [3, 21]（建议不小于4）
    },
  //"Cryptogram": {
  //  "CryptoType": "SM2", // 密码加密算法：MD5、SM2、SM4
  //  "PublicKey": "E6651AD9F26FDA073D48AFC5020C979C2A864CE3386D660F0B1158EA12CD451DFB63726B5392D310275EB2753A05BADB674CAD4CBB27C1310A952F5EADC866D8", // 公钥
  //  "PrivateKey": "716776CD86D528D4DDFECD453C5B100B7C7A709129A727F4B49B774863F1C1F2" // 私钥
  //}
}