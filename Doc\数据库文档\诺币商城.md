# 积分商城数据库设计文档

[TOC]

## MallOrder 积分商城订单表

| 序号 | 列名 | 列类型 | 列说明 |
|-|-|-|-| 
|1|Id|bigint|Id|
|2|ShopId|bigint|店铺Id|
|3|OrderCode|bigint|订单编号|
|4|CreateTime|datetime|创建时间|
|5|UpdateTime|datetime|更新时间|
|6|IsDelete|bit|软删除|
|7|UserId|bigint|用户Id|
|8|OrderCoin|decimal(18,2)|订单诺币价格|

## MallOrderItem 积分商城订单明细表

| 序号 | 列名 | 列类型 | 列说明 |
|-|-|-|-|
|1|Id|bigint|Id|  
|2|OrderId|bigint|订单Id|
|3|ProductId|bigint|商品Id|
|4|Quantity|int|商品数量|
|5|ProductName|nvarchar(100)|商品名称|
|6|UnitCoin|decimal(18,2)|商品价格|
|7|NuoCoin|decimal(18,2)|诺币现价|
|8|SettlementPrice|decimal(18,2)|结算价格|
|9|Code|bigint|券码编号|
|10|ExpirationDate|datetime|商品核销截止时间|
|11|VerificationStatus|int|状态|
|12|CreateTime|datetime|创建时间|
|13|UpdateTime|datetime|更新时间|
|14|IsDelete|bit|软删除|
|15|UserId|bigint|用户Id|
|16|ShopId|bigint|店铺Id|
|17|ProductUnit|nvarchar(255)|单位:(1杯)、(1 份)|
|18|ProductItemId|bigint|商品规格Id|
|19|OrderCoin|decimal(18,2)|订单诺币价格|
|20|VerificationDate|datetime|券码核销时间|
|21|ProductPic|nvarchar(200)|商品图片|
|22|ProductItemName|nvarchar(255)|商品规格|
|23|SkuCode|varchar(18)|SKU编码|

## MallProduct 积分商城商品表

| 序号 | 列名 | 列类型 | 列说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|ShopId|bigint|店铺Id|  
|3|Name|nvarchar(100)|商品名称|
|4|ShopTypeId|bigint|商品分类|
|5|Pic|nvarchar(255)|商品图片|
|6|Unit|nvarchar(100)|商品单位|
|7|UnitCoin|decimal(18,2)|商品价格|
|8|Specs|nvarchar(100)|商品规格|
|9|DiscountCoin|decimal(18,2)|诺币价|
|10|ValidDays|int|有效期天数|
|11|ProductBeginTime|datetime|商品生效开始时间|
|12|ProductEndTime|datetime|商品生效结束时间|
|13|Stock|int|商品库存|
|14|Sales|int|商品销量|
|15|OrderNo|int|排序|
|16|Remark|nvarchar(256)|备注|
|17|RulePic|nvarchar(255)|使用规则图片|
|18|Status|int|状态| 
|19|CreateTime|datetime|创建时间|
|20|UpdateTime|datetime|更新时间|
|21|IsDelete|bit|软删除|
|22|CreateUserId|bigint|创建者Id|
|23|UpdateUserId|bigint|修改者Id|

## MallProductItem 积分商城商品规格表

| 序号 | 列名 | 列类型 | 列说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|ProductId|bigint|商品id(主表id)|
|3|ItemName|nvarchar(100)|规格名称|
|4|UnitCoin|decimal(18,2)|商品价格|
|5|NuoCoin|decimal(18,2)|诺币价格|
|6|SettlementPrice|decimal(18,2)|结算价格|
|7|ItemStock|int|库存|
|8|Unit|nvarchar(255)|单位:(1杯)、(1 份)|  
|9|SkuCode|nvarchar(18)|SKU编码| 
|10|CreateTime|datetime|创建时间|
|11|UpdateTime|datetime|更新时间|
|12|IsDelete|bit|软删除|
|13|CreateUserId|bigint|创建者Id|
|14|UpdateUserId|bigint|修改者Id|
|15|Version|int|乐观锁|

## MallShop 店铺信息表 

| 序号 | 列名 | 列类型 | 列说明 |
|-|-|-|-|
|1|Id|bigint|Id|
|2|Banner|nvarchar(512)|banner图|
|3|Name|nvarchar(256)|名称|  
|4|Address|nvarchar(256)|地址|
|5|Phone|nvarchar(16)|商家电话|
|6|Contacts|nvarchar(16)|商家联系人|
|7|AdminId|bigint|店铺专员Id|
|8|OrderNo|int|排序|
|9|Remark|nvarchar(128)|备注|
|10|Status|int|状态|
|11|StartTime|nvarchar(255)|开始营业时间点|
|12|EndTime|nvarchar(255)|结束营业时间点|
|13|Weeks|nvarchar(255)|营业日|
|14|CreateTime|datetime|创建时间|
|15|UpdateTime|datetime|更新时间|
|16|CreateUserId|bigint|创建者Id|
|17|UpdateUserId|bigint|修改者Id| 
|18|IsDelete|bit|软删除|

