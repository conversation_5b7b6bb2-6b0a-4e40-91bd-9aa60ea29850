{
    "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",

    "SpecificationDocumentSettings": {
        "DocumentTitle": "HR共享社区接口",
        "GroupOpenApiInfos": [
            {
                "Group": "Default",
                "Title": "HR社区后端接口",
                "Description": "一个关注HR成长，赋能HR美好生活的资源圈。<br/>",
                "Version": "1.0.0"
            },
            {
                "Group": "All Groups",
                "Title": "所有接口",
                "Description": "一个关注HR成长，赋能HR美好生活的资源圈。<br/>",
                "Version": "1.0.0"
            }
        ],
        "EnableAllGroups": true,
        "LoginInfo": {
            "Enabled": false, // 是否开启Swagger登录
            "CheckUrl": "/api/swagger/checkUrl",
            "SubmitUrl": "/api/swagger/submitUrl",
            "UserName": "admin",
            "Password": "000000"
        }
    }
}