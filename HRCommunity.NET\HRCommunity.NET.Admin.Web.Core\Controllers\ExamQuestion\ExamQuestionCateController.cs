﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 教材树服务（废弃）
/// </summary>
[ApiDescriptionSettings("试题（废弃）", Order = 500)]
public class ExamQuestionCateController : IDynamicApiController
{
    private readonly IExamQuestionCateService _examQuestionCateService;
    public ExamQuestionCateController(IExamQuestionCateService examQuestionCateService)
    {
        _examQuestionCateService = examQuestionCateService;
    }

    /// <summary>
    /// 获取学科列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取学科列表")]
    public async Task<List<ExamQuestionSubject>> GetSubjectDropDownList()
    {
        return await _examQuestionCateService.SubjectList();
    }

    /// <summary>
    /// 获取教材树列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取教材树列表")]
    public async Task<List<ExamQuestionCateListOutput>> List(ExamQuestionCateListInput input)
    {
        return await _examQuestionCateService.List(input);
    }

    /// <summary>
    /// 增加教材树
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加教材树")]
    public async Task<bool> AddCate(AddExamQuestionCateInput input)
    {
        return await _examQuestionCateService.Add(input);
    }

    /// <summary>
    /// 更新教材树
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新教材树")]
    public async Task<bool> UpdateCate(UpdateExamQuestionCateInput input)
    {
        return await _examQuestionCateService.Update(input);
    }


    ///// <summary>
    ///// 删除教材树
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[ApiDescriptionSettings(Name = "Delete"), HttpPost]
    //[DisplayName("删除教材树")]
    //public async Task<bool> DeleteCate(BaseIdInput input)
    //{
    //    return await _examQuestionCateService.Delete(input);
    //}

    /// <summary>
    /// 设置教材树状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置教材树状态")]
    public async Task<bool> SetStatus(ExamQuestionCateStatusInput input)
    {
        return await _examQuestionCateService.SetStatus(input);
    }
}
