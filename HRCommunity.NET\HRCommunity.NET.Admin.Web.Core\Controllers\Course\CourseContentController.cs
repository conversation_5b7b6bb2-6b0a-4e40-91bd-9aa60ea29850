﻿using static SKIT.FlurlHttpClient.Wechat.Api.Models.ComponentTCBBatchGetEnvironmentIdResponse.Types;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 课程 - 内容服务
/// </summary>
[ApiDescriptionSettings("Course", Order = 490)]
public class CourseContentController : IDynamicApiController
{
    private readonly ICourseContentService _courseContentService;
    //private readonly ICourseCommentService _courseCommentService;
    private readonly ICourseCateService _courseCateService;
    private readonly IMessageNoticeService _messageNoticeService;
    public CourseContentController(ICourseContentService courseContentService,
                                   //ICourseCommentService courseCommentService,
                                   ICourseCateService courseCateService,
                                   IMessageNoticeService messageNoticeService)
    {
        _courseContentService = courseContentService;
        //_courseCommentService = courseCommentService;
        _courseCateService = courseCateService;
        _messageNoticeService = messageNoticeService;
    }


    /// <summary>
    /// 获取课程内容分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取课程类型分页列表")]
    public async Task<SqlSugarPagedList<PageCourseContentOutput>> Page(PageCourseContentInput input)
    {
        return await _courseContentService.Page(input);
    }


    ///// <summary>
    ///// 导出内容数据
    ///// </summary>
    ///// <returns></returns>
    //[ApiDescriptionSettings(Name = "ExportCourse"), NonUnify]
    //[DisplayName("导出内容数据")]
    //public async Task<IActionResult> ExportCourse(PageCourseContentInput input)
    //{

    //    input.Page = 1;
    //    input.PageSize = int.MaxValue;
    //    var logOpList = await _courseContentService.Page(input);


    //    // 将数据映射到DTO
    //    var dtoList = logOpList.Items.Select(item =>
    //    {
    //        var basicDataList = _courseContentService.PageBasicDataList(new PageDataListInput() { Id = item.Id, Page = 1, PageSize = int.MaxValue, StartTime = DateTime.Now.AddYears(-10), EndTime = DateTime.Now }, false).Result.Items;
    //        var interactDataList = _courseContentService.PageInteractDataList(new PageDataListInput() { Id = item.Id, Page = 1, PageSize = int.MaxValue, StartTime = DateTime.Now.AddYears(-10), EndTime = DateTime.Now }, false).Result.Items;

    //        var clicks = basicDataList.Sum(v => v.Clicks);
    //        var relation = interactDataList.Sum(v => v.Relation);
    //        var comment = interactDataList.Sum(v => v.Comment);
    //        var commentUser = interactDataList.Sum(v => v.CommentUser);
    //        var visitorViews = basicDataList.Sum(v => v.VisitorsViews);
    //        var visitorClicks = basicDataList.Sum(v => v.VisitorsClicks);

    //        return new PageCourseContentOutputDto
    //        {
    //            OutId = item.OutId,
    //            UserOutId = item.UserOutId,
    //            NickName = item.NickName,
    //            Type = item.Type == CourseTypeEnum.Short ? "动态" : "经验",
    //            //CateIdListString = string.Join(",", _courseCateService.GetDropDownList().Result.Where(v => item.CateIdList.Contains(v.Id)).Select(v => v.Name)),
    //            Title = item?.Title,
    //            Views = item.Views,
    //            Clicks = clicks,
    //            Sraise = item.Sraise,
    //            Collect = item.Collect,
    //            Relation = relation,
    //            Comment = comment,
    //            CommentUser = commentUser,
    //            interact = item.Collect + relation + item.Sraise + comment,
    //            exposureRate = item.Views == 0 ? 0 : Math.Round((double)clicks / item.Views, 2),
    //            visitorViews = visitorViews,
    //            visitorClicks = visitorClicks,
    //            visitorRate = visitorViews == 0 ? 0 : Math.Round((double)visitorClicks / visitorViews, 2),
    //        };
    //    }).ToList();

    //    IExcelExporter excelExporter = new ExcelExporter();
    //    var res = await excelExporter.ExportAsByteArray(dtoList);

    //    var memoryStream = new MemoryStream(res);

    //    var byteContent = memoryStream.ToArray();

    //    var result = new FileContentResult(byteContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    //    {
    //        FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + ".xlsx"
    //    };
    //    return result;

    //    //return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + "内容数据.xlsx" };
    //}

    /// <summary>
    /// 增加课程内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加课程内容")]
    public async Task<bool> AddContent(AddCourseContentInput input)
    {
        var id = await _courseContentService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新课程内容
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新课程内容")]
    public async Task<bool> UpdateContent(UpdateCourseContentInput input)
    {
        return await _courseContentService.Update(input);
    }


    ///// <summary>
    ///// 删除内容
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[ApiDescriptionSettings(Name = "Delete"), HttpPost]
    //[DisplayName("删除课程内容")]
    //public async Task<bool> DeleteContent(CourseContentInput input)
    //{
    //    return await _courseContentService.Delete(input);
    //}

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task<bool> SetStatus(StatusCourseContentInput input)
    {
        return await _courseContentService.SetStatus(input);
    }

    /// <summary>
    /// 修改课程是否公开展示
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("修改课程是否公开展示")]
    public async Task<bool> SetPublic(CoursePublicInput input)
    {
        return await _courseContentService.SetPublic(input);
    }

    /// <summary>
    /// 获取课程内容详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取课程内容详情")]
    public async Task<CourseDetailOutput> Detail(CourseContentInput input)
    {
        return await _courseContentService.GetDetail(input);
    }

    ///// <summary>
    ///// 内容详情数据-数据概览
    ///// </summary>
    ///// <param name="id"></param>
    ///// <returns></returns>
    //[DisplayName("内容详情数据-数据概览")]
    //public async Task<CourseDetailOverviewOutput> GetDetailOverview(long id)
    //{
    //    return await _courseContentService.GetOverview(id);
    //}

    ///// <summary>
    ///// 内容详情-获取课程一级评论数据列表
    ///// </summary>
    ///// <param name="input"></param>
    ///// <returns></returns>
    //[DisplayName("获取课程一级评论数据列表")]
    //public async Task<SqlSugarPagedList<CourseCommentOutput>> FirstCommentPage(PageCourseCommentInput input)
    //{
    //    return await _courseCommentService.Page(input);
    //}

    ///// <summary>
    ///// 内容详情-获取课程二级评论数据列表
    ///// </summary>
    ///// <param name="CommentId"></param>
    ///// <returns></returns>
    //[DisplayName("获取课程二级评论数据列表")]
    //public async Task<List<CourseCommentOutput>> GetSecondCommentList(long CommentId = 0)
    //{
    //    return await _courseCommentService.GetSecondList(CommentId);
    //}

    /// <summary>
    /// 课程数据 - 数据分析 - 学习数据(总订阅人数、总学习人数)
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("课程数据 - 数据分析 - 学习数据(总订阅人数、总学习人数)")]
    public async Task<CourseDataOverviewOutput> GetStudyDataOverview(long id)
    {
        return await _courseContentService.GetStudyDataOverview(id);
    }

    /// <summary>
    /// 课程数据 - 数据分析 - 学习趋势图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程数据 - 数据分析 - 学习趋势图")]
    public async Task<SqlSugarPagedList<CoursePageBaseDataListOutput>> PageStudyDataList(PageDataListInput input)
    {
        return await _courseContentService.PageStudyDataList(input);
    }

    /// <summary>
    /// 课程数据 - 数据分析 - 学习详情列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程数据 - 数据分析 - 学习详情列表")]
    public async Task<SqlSugarPagedList<CoursePageBasicDataListOutput>> PageBasicDataList(PageDataListInput input)
    {
        return await _courseContentService.PageBasicDataList(input);
    }

    /// <summary>
    /// 课程数据 - 数据分析 - 销售情况 (支付人数、支付金额、订阅量)
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程数据 - 数据分析 - 销售情况 (支付人数、支付金额、订阅量)")]
    public async Task<CourseOrderDataOverviewOutput> OrderDataOverview(PageDataListInput input)
    {
        return await _courseContentService.GetOrderDataOverview(input);
    }

    /// <summary>
    /// 课程数据 - 数据分析 - 销售情况 - 交易趋势图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("程数据 - 数据分析 - 销售情况 - 交易趋势图")]
    public async Task<List<CourseOrderPageBaseDataListOutput>> PageOrderDataList(PageDataListInput input)
    {
        return await _courseContentService.PageOrderDataList(input);
    }

    /// <summary>
    /// 课程数据 - 数据分析 - 销售情况 - 交易列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程数据 - 数据分析 - 销售情况 - 交易列表")]
    public async Task<SqlSugarPagedList<CourseOrderPageBaseDataListOutput>> PageOrderBasicDataList(PageDataListInput input)
    {
        return await _courseContentService.PageOrderBasicDataList(input);
    }

    /// <summary>
    /// 课程数据 - 学员列表 - 学员详情列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("课程数据 - 学员列表 - 学员详情列表")]
    public async Task<SqlSugarPagedList<CoursePageBasicDataListOutput>> UserSubPageBasicDataList(PageDataListInput input)
    {
        return await _courseContentService.UserSubPageBasicDataList(input);
    }


    /// <summary>
    /// 课程 - 添加用户（赠送课程）
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "AddUser"), HttpPost]
    [DisplayName("课程 - 添加用户（赠送课程）")]
    public async Task<bool> AddUser(AddUserContentInput input)
    {
        input.SourceType = 1;
        var ret = await _courseContentService.AddUser(input);

        //插入平台通知
        await Task.Run(async () =>
        {
            await _messageNoticeService.AddMsg(new AddMsgNoticeInput()
            {
                UserIdList = input.UserIdList,
                Type = 2,
                SourceId = input.CourseId,
            });
        });
        return ret;
    }

    /// <summary>
    /// 导出订阅列表
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Export"), NonUnify]
    [DisplayName("导出订阅列表")]
    public async Task<IActionResult> ExportUserSubPage(PageDataListInput input)
    {
        var ret = await _courseContentService.GetExportList(input);

        var excelExporter = new ExcelExporter();

        var res = await excelExporter.ExportAsByteArray(ret.Item2);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"{ret.Item1.CourseName}课程订阅列表.xlsx" };
    }

    /// <summary>
    /// 认证课批量团报导入
    /// </summary>Required]
    /// <param name="file"></param>
    /// <param name="courseId"></param>
    /// <returns></returns>
    [DisplayName("认证课批量团报导入")]
    public async Task<CertCourseResultImportOutput> CourseCertUploadFile([Required] IFormFile file, [FromQuery] long courseId)
    {
        return await _courseContentService.CourseCertUploadFile(file, courseId);
    }

}
