# 直播聊天室数据库设计文档

[toc]

## 聊天室表不必设计，直播Id做为聊天室Id即可

## ~~ChatRoomUser 聊天室成员表~~

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id,PrimaryKey|
|LiveId|bigint|直播Id/聊天室Id|
|UserId|bigint|用户Id|
|NickName|nvarchar(32)|昵称|
|Avatar|nvarchar(512)|头像|
|Role|int|身份类型 1讲师 2普通用户|
|Status|int|状态|
|CreateTime|datetime|创建时间|
|UpdateTime|datetime|更新时间|
|EntryTime|datetime|首次进入时间|
|IsDelete|bit|软删除|

## ChatRoomAway 聊天室进入离开记录表

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id,PrimaryKey|
|LiveId|bigint|直播Id/聊天室Id|
|UserId|bigint|用户Id|
|JoinTime|datetime|进入时间|
|ExitTime|datetime|离开时间|
|Duration|int|本次在线时长，秒|

## ~~ChatRoomMessage 聊天室消息记录表~~

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id,PrimaryKey|
|LiveId|bigint|直播Id/聊天室Id|
|UserId|bigint|用户Id|
|NickName|nvarchar(32)|昵称|
|Avatar|nvarchar(512)|头像|
|ReplyUserId|bigint|被回复人Id|
|ReplyNickName|nvarchar(32)|被回复人昵称|
|Role|int|身份类型 1讲师 2普通用户|
|Type|int|消息类型 1系统消息 2普通消息|
|Message|nvarchar(512)|消息内容|
|Status|int|状态|
|AuditStatus|int|审核状态 0审核中 1审核通过 2审核不通过（违规）| 
|Reason|nvarchar(256)|审核不通过原因| 
|CreateTime|datetime|创建时间|
|UpdateTime|datetime|更新时间|
|IsDelete|bit|软删除|

## ~~ChatRoomMessageRead 聊天室成员消息阅读记录表~~

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id,PrimaryKey|
|LiveId|bigint|直播Id/聊天室Id|
|UserId|bigint|用户Id|
|MessageId|bigint|消息Id|
|CreateTime|datetime|阅读时间|