﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 营销-邀请有礼
/// </summary>
[ApiDescriptionSettings("营销-邀请有礼", Order = 480)]
public class InvitationRewardController : IDynamicApiController
{
    private readonly IInvitationRewardService _invRedService;
    public InvitationRewardController(IInvitationRewardService invRedService)
    {
        _invRedService = invRedService;
    }

    /// <summary>
    /// 邀请有礼列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取邀请有礼分页列表")]
    public async Task<SqlSugarPagedList<PageInviteActivityOutput>> Page(PageInviteActivityInput input)
    {
        return await _invRedService.Page(input);
    }

    /// <summary>
    /// 获取邀请有礼详情
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("获取邀请有礼详情")]
    public async Task<InvActDetailOutput> GetDetail(long Id)
    {
        return await _invRedService.GetDetail(Id);
    }

    /// <summary>
    /// 增加邀请有礼
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加邀请有礼")]
    public async Task<bool> Add(AddInviteActivityInput input)
    {
        var id = await _invRedService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新邀请有礼
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新邀请有礼")]
    public async Task<bool> Update(UpdateInviteActivityInput input)
    {
        return await _invRedService.Update(input);
    }

    /// <summary>
    /// 过期邀请有礼活动
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("过期邀请有礼活动")]
    public async Task<bool> SetExpires(BaseIdInput input)
    {
        return await _invRedService.SetExpires(input);
    }

    /// <summary>
    /// 数据-每日新增折线图
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("数据-每日新增折线图")]
    public async Task<List<InvRewardPeopleDataListOutput>> PeopleDataList(BaseIdInput input)
    {
        return await _invRedService.PeopleDataList(input);
    }

    /// <summary>
    /// 数据-邀请人数记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("数据-邀请人数记录")]
    public async Task<SqlSugarPagedList<PageInviteRecordMainOutput>> RecordMainPage(PageInviteRecordMainInput input)
    {
        return await _invRedService.RecordMainPage(input);
    }

    /// <summary>
    /// 数据-被邀请人员记录
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("数据-被邀请人员记录")]
    public async Task<SqlSugarPagedList<PageInviteRecordPeopleOutput>> RecordPeoplePage(PageInviteRecordPeopleInput input)
    {
        return await _invRedService.RecordPeoplePage(input);
    }


    /// <summary>
    /// 导出邀请有礼数据
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Export"), NonUnify]
    [DisplayName("导出邀请有礼数据")]
    public async Task<IActionResult> ExportAct(PageInviteRecordMainInput input)
    {
        var (name,exportList) = await _invRedService.GetExportList(input);

        var excelExporter = new ExcelExporter();

        var res = await excelExporter.ExportAsByteArray(exportList);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"{name}邀请信息.xlsx" };
    }
}
