﻿using HRCommunity.NET.Application;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 直播、IM回调
/// </summary>
[AllowAnonymous]
[ApiDescriptionSettings("Callback", Order = 500)]
public class CallbackController : IDynamicApiController
{
    #region 声明
    private readonly ILiveSessionService _liveSessionService;
    //private readonly IHttpClientFactory _httpClientFactory;
    //private readonly IMediator _mediator;
    private readonly ILogger _logger;
    private readonly ILiveStatService _chatRoomService;
    private readonly ITencentLiveService _tencentLiveService;
    private readonly IGrowthActionService _growthActionService;
    private readonly TencentLiveConfig _liveOptions;
    private static readonly ImBaseResponse DefaultTimCallbackResponse = new ImBaseResponse
    {
        ActionStatus = "OK",
        ErrorInfo = string.Empty,
        ErrorCode = 0
    };

    private static readonly object DefaultTliveCallbackResponse = new
    {
        code = 0
    };
    private readonly ISysCacheService _sysCacheService;

    public CallbackController(
        ILiveSessionService liveSessionService,
        ILiveStatService chatRoomService,
        ITencentLiveService tencentLiveService,
        //IHttpClientFactory httpClientFactory,
        //IMediator mediator,
        ILoggerFactory logger,
        IOptions<TencentLiveConfig> liveOptions,
        IGrowthActionService growthActionService,
        ISysCacheService sysCacheService)
    {
        _liveSessionService = liveSessionService;
        _chatRoomService = chatRoomService;
        _tencentLiveService = tencentLiveService;
        _liveOptions = liveOptions.Value;
        //_httpClientFactory = httpClientFactory;
        //_mediator = mediator;
        _logger = logger.CreateLogger("CallbackController");
        _growthActionService = growthActionService;
        _sysCacheService = sysCacheService;
    }
    #endregion

    #region 直播
    /// <summary>
    /// 腾讯即时通信IM回调
    /// </summary>
    /// <param name="query"></param>
    /// <returns></returns>
    [HttpPost]
    [UnifyResult(typeof(ImBaseResponse))]
    public async Task<object> Tim([FromQuery] ImCallbackRequestQuery query)
    {
        if (query.SdkAppid != _liveOptions.ImAppId)
        {
            //await _logs.Debug(LogModule, new { Title = "非指定AppId的回调", Query = query }.ToJsonString());
            return DefaultTimCallbackResponse;
        }

        try
        {
            using var ms = new MemoryStream();
            await App.HttpContext.Request.Body.CopyToAsync(ms);
            var b = ms.ToArray();
            var callbackJson = Encoding.UTF8.GetString(b);
            var bodyToken = JToken.Parse(callbackJson);

            if (bodyToken.HasValues)
            {
                //ImCallbackRequestBody bodyModel = null;
                switch (query.CallbackCommand)
                {
                    ////创建群组之后回调
                    //case "Group.CallbackAfterCreateGroup":
                    //    bodyModel = bodyToken.ToObject<CallbackAfterCreateGroup>();
                    //    break;
                    //群成员离开之后回调
                    case "Group.CallbackAfterMemberExit":
                        //bodyModel = bodyToken.ToObject<CallbackAfterMemberExit>();
                        var memberExitModel = JSON.Deserialize<CallbackAfterMemberExit>(callbackJson);
                        //更新进入聊天室记录
                        var exitInput = new ExitChatRoomAwayInput()
                        {
                            LiveId = Convert.ToInt64(memberExitModel.GroupId),
                            UserIdList = memberExitModel.ExitMemberList.Select(x => Convert.ToInt64(x.Member_Account)).ToList()
                        };

                        await _chatRoomService.ExitRoom(exitInput);
                        break;
                    //新成员入群之后回调
                    case "Group.CallbackAfterNewMemberJoin":
                        //bodyModel = bodyToken.ToObject<CallbackAfterNewMemberJoin>();
                        var memberJoinModel = JSON.Deserialize<CallbackAfterNewMemberJoin>(callbackJson);
                        //插入进入直播间记录
                        var joinInput = new AddChatRoomAwayInput()
                        {
                            LiveId = Convert.ToInt64(memberJoinModel.GroupId),
                            UserIdList = memberJoinModel.NewMemberList.Select(x => Convert.ToInt64(x.Member_Account)).ToList()
                        };

                        await _chatRoomService.JoinRoom(joinInput);

                        // 记录直播间多少人围观，并记录直播状态
                        var redisKey = $"LiveVisitorClick:{memberJoinModel.GroupId}";
                        var redisStatusKey = $"LiveStatus:{memberJoinModel.GroupId}";

                        if (_sysCacheService.ExistKey(redisStatusKey) && _sysCacheService.Get<bool>(redisStatusKey) == false)
                        {
                            // 直播已关闭，不记录访问人数
                            break;
                        }

                        // 记录访问人数
                        if (!_sysCacheService.ExistKey(redisKey))
                        {
                            _sysCacheService.Set(redisKey, 1, TimeSpan.FromDays(365));
                        }
                        else
                        {
                            _sysCacheService.Increment(redisKey);
                        }

                        // 设置直播状态为开启
                        _sysCacheService.Set(redisStatusKey, true, TimeSpan.FromDays(365));

                        //完成观看直播任务，正常到课
                        await Task.Run(() =>
                        {
                            joinInput.UserIdList.ForEach(async x =>
                            {
                                //成长值任务-观看直播-每日任务
                                await _growthActionService.GiveGrowth(GrowthActionEnum.Live, joinInput.LiveId, x);
                            });
                        });
                        break;
                    //群内发言之后回调
                    case "Group.CallbackAfterSendMsg":
                        //var messbody = bodyToken.ToObject<CallbackAfterSendMsg>();

                        _logger.LogInformation("---------------群内发言之后回调---------------------");
                        _logger.LogInformation("messbody： " + JSON.Serialize(bodyToken));

                        await _chatRoomService.HandleChatMessage(bodyToken.ToString());

                        break;
                    ////群组解散之后回调
                    //case "Group.CallbackAfterGroupDestroyed":
                    //    bodyModel = bodyToken.ToObject<CallbackAfterGroupDestroyed>();
                    //    break;
                    /*
                     在线状态变更回调 2024年8月15日
                        参考链接https://cloud.tencent.com/document/product/269/2570
                        针对杀掉进程，直接关闭页面，断网等问题的观看时长回调
                     */
                    case "State.StateChange":
                        CallbackStateChange callbackState = bodyToken.ToObject<CallbackStateChange>();
                        if (callbackState.Info.Reason.ToUpper() == "LINKCLOSE")
                        {
                            try
                            {
                                //更新进入聊天室记录
                                var KillInput = new ExitChatRoomAwayInput()
                                {
                                    //没有群组的信息
                                    //LiveId = Convert.ToInt64(memberExitModel.GroupId),
                                    UserIdList = new List<long> { Convert.ToInt64(callbackState.Info.To_Account) }
                                };

                                await _chatRoomService.KillChat(KillInput);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError("直播群组回调，在线状态变更回调错误：" + ex.Message);
                            }
                        }

                        _logger.LogInformation("---------------在线状态变更回调开始---------------------");
                        _logger.LogInformation("bodyModel： " + JSON.Serialize(callbackState));
                        _logger.LogInformation("---------------在线状态变更回调结束---------------------");

                        break;
                    default:
                        //await _logs.Debug(LogModule, new { Title = "未监听类型", Body = bodyString, Query = query }.ToJsonString());
                        break;
                }
                //if (bodyModel != null)
                //{
                //    //_ = _mediator.Publish(new EventModels.LiveCallback.ImGroupEvent { Data = bodyModel });
                //}
                //else
                //{
                //    //await _logs.Debug(LogModule, new { Title = "IM回调Body转换失败", Body = bodyString, Query = query }.ToJsonString());
                //}
            }
            else
            {
                //await _logs.Error(LogModule, new { Title = "IM回调Body解析异常", Body = bodyString, Query = query }.ToJsonString());
            }
        }
        catch (Exception e)
        {
            //await _logs.Error(LogModule, new { Title = "IM回调处理出错", Query = query }.ToJsonString(), e);
        }

        return DefaultTimCallbackResponse;
    }

    /// <summary>
    /// 腾讯云直播回调
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [UnifyResult(typeof(object))]
    public async Task<object> Tlive()
    {
        using var ms = new MemoryStream();
        await App.HttpContext.Request.Body.CopyToAsync(ms);
        var b = ms.ToArray();
        var bodyString = Encoding.UTF8.GetString(b);
        //await _logs.Debug($"腾讯云直播回调:{bodyString}");
        // 推断流事件通知 https://cloud.tencent.com/document/product/267/47025
        try
        {
            var token = JToken.Parse(bodyString);
            var appName = token.Value<string>("appname"); //推流路径 trtc_(实时音视频)SDKAppID

            #region 向Test环境转发回调
            //if (Request.Host.Host == "test-api.noahhr.cn" && appName == "hrlive")
            //{
            //    try
            //    {
            //        using var client = _httpClientFactory.CreateClient();
            //        await client.PostAsync("https://test-api.noahhr.cn/api/callback/tlive", new StringContent(bodyString));
            //    }
            //    catch (Exception ex)
            //    {
            //        Console.WriteLine(ex.Message);
            //    }
            //    return DefaultTliveCallbackResponse;
            //}
            #endregion

            var pushDomain = token.Value<string>("app"); //推流域名
            var dataExpTime = token.Value<long>("t"); //消息过期时间 秒时间戳 10分钟
            var dataSign = token.Value<string>("sign"); //安全签名 MD5(key+t) 小写 key=直播回调密钥 t=消息过期时间

            //_logger.LogInformation("------------------------------------");
            //_logger.LogInformation("pushDomain " + pushDomain);
            //_logger.LogInformation("dataExpTime " + dataExpTime);
            //_logger.LogInformation("dataSign " + dataSign);
            //_logger.LogInformation("------------------------------------");

            if (token.HasValues &&
                pushDomain == _liveOptions.PushStreamDomin &&
                dataExpTime > 0 &&
                dataSign == StringExpand.GetMD5($"{_liveOptions.LiveCallbackSecret}{dataExpTime}").ToLower())
            {
                var eventTime = token.Value<long>("event_time"); //消息发送时间 秒时间戳
                var eventType = token.Value<int>("event_type"); //事件类型 0=断流 1=推流
                var streamId = token.Value<string>("stream_id"); //流ID 直播间ID

                //_logger.LogInformation("------------------------------------");
                //_logger.LogInformation("eventTime " + eventTime);
                //_logger.LogInformation("eventType " + eventType);
                //_logger.LogInformation("streamId " + streamId);
                //_logger.LogInformation("------------------------------------");

                switch (eventType)
                {
                    case 0:
                        //直播断流
                        //_ = _mediator.Publish(new EventModels.LiveCallback.LiveSuspendEvent { StreamId = streamId, SuspendAt = eventTime });
                        await _liveSessionService.UpdateDropLiveTime(new UpdateLiveSessionInput()
                        {
                            Id = Convert.ToInt64(streamId),
                            DropLiveTime = DateTime.Now
                        });

                        break;
                    case 1:
                        //创建聊天室
                        long liveId = Convert.ToInt64(streamId);
                        var live = await _liveSessionService.GetDetail(new LiveSessionInput { Id = liveId });
                        if (live != null && live.LiveStatus == LiveStatusEnum.NotStarted)
                        {
                            var ret = await _tencentLiveService.CreateImGroup(new CreateImGroup()
                            {
                                GroupId = liveId.ToString(),
                                Name = live.Name.Length > 10 ? live.Name[..10] : live.Name,
                                Type = "AVChatRoom",
                            });
                            _logger.LogWarning($"创建聊天室【{liveId}-{live.Name}】，返回结果：{JSON.Serialize(ret)}");
                        }

                        //直播推流
                        UpdateLiveSessionInput input = new UpdateLiveSessionInput();
                        input.Id = liveId;
                        input.LiveStatus = LiveStatusEnum.LiveStreaming;
                        input.PushLiveTime = DateTime.Now;
                        await _liveSessionService.UpdateLiveStatus(input);
                        break;
                    default:
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            //await _logs.Error("腾讯云直播回调", ex);
        }
        return DefaultTliveCallbackResponse;
    }
    #endregion

    #region 测试
    [HttpGet]
    public async void test()
    {
        //更新进入聊天室记录
        var KillInput = new ExitChatRoomAwayInput()
        {
            //没有群组的信息
            //LiveId = Convert.ToInt64(memberExitModel.GroupId),
            UserIdList = new List<long> { Convert.ToInt64("100009") }
        };

        await _chatRoomService.KillChat(KillInput);
    }
    #endregion

}
