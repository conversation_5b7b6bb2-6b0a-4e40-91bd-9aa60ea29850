namespace HRCommunity.NET.Application;

/// <summary>
/// 运营后台伪登录Token服务接口
/// </summary>
public interface ISysImpersonateTokenService : ITransient
{
    /// <summary>
    /// 生成伪登录Token
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<ImpersonateTokenOutput> GenerateTokenAsync(GenerateImpersonateTokenInput input);

    /// <summary>
    /// 分页查询伪登录Token
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    Task<SqlSugarPagedList<ImpersonateTokenOutput>> GetPageAsync(ImpersonateTokenPageInput input);

    /// <summary>
    /// 获取伪登录Token详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<ImpersonateTokenOutput> GetDetailAsync(long id);

    /// <summary>
    /// 删除伪登录Token
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task DeleteAsync(long id);

    /// <summary>
    /// 验证并使用伪登录Token（客户端调用）
    /// </summary>
    /// <param name="token"></param>
    /// <param name="clientIp"></param>
    /// <param name="userAgent"></param>
    /// <returns></returns>
    Task<UserInfo> ValidateAndUseTokenAsync(string token, string clientIp, string userAgent);
}
