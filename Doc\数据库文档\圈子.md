# 圈子数据库设计文档

[toc]


## CircleCategory 圈子分类表

| 列名 | 列类型 | 列说明 |
|-|-|-|-| 
|Id|bigint|Id,PrimaryKey|
|Name|varchar(128)|分类名称，限制输入100字，验重|
|Status|int|状态| 
|OrderNo|int|排序,正序|
|CreateTime|datetime|创建时间|
|UpdateTime|datetime|更新时间|
|IsDelete|bit|软删除|
|CreateUserId|bigint|创建者Id|
|UpdateUserId|bigint|修改者Id|

## CircleContent 圈子内容表

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|主键Id|
|OutId|bigint|外展Id，Identity(100000,1)|
|Type|int|类型 1动态 2经验|
|CateId|bigint|圈子分类Id，圈子分类表主键Id|
|UserId|bigint|发布人Id，用户信息表主键Id|
|Question|nvarchar(2000)|想法或提问，Type=1维护|
|Pictures|nvarchar(2000)|组图，Type=1维护，最多9张图片，存储形式可以讨论讨论？|
|Title|nvarchar(128)|内容标题，Type=2维护，最大输入100字|
|Content|ntext|内容详情，Type=2维护|
|SmallContent|nvarchar(128)|内容详情简介，Type=2维护，保存时从Content截取50个字|
|RelationId|bigint|引用Id，圈子内容主键Id|
|Status|int|状态| 
|AuditStatus|int|审核状态 0审核中 1审核通过 2审核不通过（违规）| 
|Reason|nvarchar(256)|审核不通过原因| 
|SourceType|int|数据来源类型 1后台 2 H5|
|OrderNo|int|排序,倒序|
|CreateTime|datetime|创建时间|
|UpdateTime|datetime|更新时间|
|IsDelete|bit|软删除|
|CreateUserId|bigint|创建者Id|
|UpdateUserId|bigint|修改者Id|

## ~~CircleContentStatistics圈子内容统计表~~
`暂时不需要这表` 

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|主键Id|
|UserId|bigint|收藏量|
|Question|nvarchar(2000)|引用量|
|Content|ntext|点赞量|
|Pictures|nvarchar(2000)|评论量|
|Title|nvarchar(100)|评论人数|

## CircleComment 圈子评论表

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id|
|CircleId|bigint|圈子内容Id|
|CommentId|bigint|圈子评论Id，默认0是一级评论（即回复内容的），大于0为二级评论（即回复评论的）|
|Content|nvarchar(128)|评论内容，最多输入100字|
|UserId|bigint|评论人Id，用户信息表主键Id|
|NickName|nvarchar(32)|评论人昵称，冗余|
|Avatar|nvarchar(512)|评论人头像，冗余|
|ReceiveUserId|bigint|被评论人Id，用户信息表主键Id|
|ReceiveNickName|nvarchar(32)|被评论人昵称，冗余|
|ReceiveAvatar|nvarchar(512)|被评论人头像，冗余|
|AuditStatus|int|审核状态 0审核中 1审核通过 2审核不通过（违规）| 
|Reason|nvarchar(256)|审核不通过原因| 
|CreateTime|datetime|创建时间，评论时间|
|UpdateTime|datetime|更新时间|
|IsDelete|bit|软删除|

## CircleSraise 圈子点赞记录表

`取消点赞物理删除数据` 
| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id|
|CircleId|bigint|圈子内容Id|
|CommentId|bigint|圈子评论Id，默认0是给圈子内容点赞，大于0为给评论点赞|
|UserId|bigint|点赞人Id，用户信息表主键Id|
|NickName|nvarchar(32)|点赞人昵称，冗余|
|Avatar|nvarchar(512)|点赞人头像，冗余|
|CreateTime|datetime|创建时间，评论时间|

## ~~CircleCommentStatistics圈子评论统计表~~
`暂时不需要这表` 

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|主键Id|
|CircleId|bigint|圈子内容Id|
|CommentId|bigint|圈子评论Id|
|Content|ntext|点赞量|

## Feedback意见反馈表

| 列名 | 列类型 | 列说明 |
|-|-|-| 
|Id|bigint|Id|
|Content|varchar(200)|反馈意见，最多输入200字|
|Pictures|nvarchar(512)|图片，最多3张图片|
|UserId|bigint|反馈人Id，用户信息表主键Id|
|CreateTime|datetime|创建时间，反馈时间|
|UpdateTime|datetime|更新时间|
|IsDelete|bit|软删除|
