﻿namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 营销-兑换码管理
/// </summary>
[ApiDescriptionSettings("营销-兑换码管理", Order = 490)]
public class RedeemCodeController : IDynamicApiController
{
    private readonly IRedeemCodeService _redeemCodeService;
    private readonly IVipCardService _vipCardService;
    public RedeemCodeController(IRedeemCodeService redeemCodeService,
                                IVipCardService vipCardService)
    {
        _redeemCodeService = redeemCodeService;
        _vipCardService = vipCardService;
    }

    /// <summary>
    /// 兑换码列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取兑换码分页列表")]
    public async Task<SqlSugarPagedList<RedeemCodePageOutput>> Page(RedeemCodePageInput input)
    {
        return await _redeemCodeService.Page(input);
    }

    /// <summary>
    /// 获取兑换码详情
    /// </summary>
    /// <param name="Id"></param>
    /// <returns></returns>
    [DisplayName("获取兑换码详情")]
    public async Task<RedeemCodeDetailOutput> GetDetail(long Id)
    {
        return await _redeemCodeService.GetDetail(Id);
    }

    /// <summary>
    /// 增加兑换码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加兑换码")]
    public async Task<bool> Add(AddRedeemCodeInput input)
    {
        var id = await _redeemCodeService.Add(input);
        return id > 0;
    }

    /// <summary>
    /// 更新兑换码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新兑换码")]
    public async Task<bool> Update(UpdateRedeemCodeInput input)
    {
        return await _redeemCodeService.Update(input);
    }

    /// <summary>
    /// 过期兑换码活动
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("过期兑换码活动")]
    public async Task<bool> SetExpires(BaseIdInput input)
    {
        return await _redeemCodeService.SetExpires(input);
    }

    /// <summary>
    /// 效果数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("效果数据分页列表")]
    public async Task<SqlSugarPagedList<RedeemCodeRecordPageOutput>> RecordPage(RedeemCodeRecordPageInput input)
    {
        return await _redeemCodeService.RecordPage(input);
    }

    /// <summary>
    /// 添加库存
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("添加库存")]
    public async Task<bool> AddStock(AddStockInput input)
    {
        return await _redeemCodeService.AddStock(input);
    }

    /// <summary>
    /// 作废单个兑换码
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("作废单个兑换码")]
    public async Task<bool> SetCancel(BaseIdInput input)
    {
        return await _redeemCodeService.SetCancel(input);
    } 

    /// <summary>
    /// 导出兑换码列表
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Export"), NonUnify]
    [DisplayName("导出兑换码列表")]
    public async Task<IActionResult> Export(RedeemCodeRecordPageInput input)
    {
        var detail = await _redeemCodeService.GetDetail(input.RedeemCodeId);
        var exportList = await _redeemCodeService.GetExportList(input);

        IExcelExporter excelExporter = new ExcelExporter();
        var res = await excelExporter.ExportAsByteArray(exportList);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"{detail.Name}效果数据.xlsx" };
    }
}
