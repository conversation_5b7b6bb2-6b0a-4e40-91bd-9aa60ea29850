﻿using Furion.DatabaseAccessor;
using HRCommunity.NET.Application;

namespace HRCommunity.NET.Admin.Web.Core.Controllers;

/// <summary>
/// 营销-活动管理
/// </summary>
[ApiDescriptionSettings("营销-活动管理", Order = 500)]
public class BrandActivityController : IDynamicApiController
{
    private readonly IUserService _sysUserService;
    private readonly IBrandActivityService _BrandActivityService;
    private readonly UserManager _userManager;
    private readonly NoahShortHelper _noahShortHelper;
    private readonly BesChannelsHelper _besChannelsHelper;
    private readonly ISysSmsTemplateService _sysSmsTemplateService;
    public BrandActivityController(IUserService sysUserService,
                                   IBrandActivityService brandActivityService,
                                   UserManager userManager,
                                   NoahShortHelper noahShortHelper,
                                   ISysSmsTemplateService sysSmsTemplateService,
                                   BesChannelsHelper besChannelsHelper)
    {
        _sysUserService = sysUserService;
        _BrandActivityService = brandActivityService;
        _userManager = userManager;
        _noahShortHelper = noahShortHelper;
        _besChannelsHelper = besChannelsHelper;
        _sysSmsTemplateService = sysSmsTemplateService;
    }

    #region 活动管理

    /// <summary>
    /// 活动列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取活动分页列表")]
    public async Task<SqlSugarPagedList<BrandActivityPageOutput>> PageActivity(BrandActivityInput input)
    {
        return await _BrandActivityService.PageActivity(input);
    }

    /// <summary>
    /// 增加活动
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加活动")]
    public async Task<bool> AddActivity(AddBrandActivityInput input)
    {
        var id = await _BrandActivityService.AddActivity(input);
        return id > 0;
    }

    /// <summary>
    /// 更新活动
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新活动")]
    public async Task<bool> UpdateActivity(UpdateBrandActivityInput input)
    {
        return await _BrandActivityService.UpdateActivity(input);
    }

    /// <summary>
    /// 设置活动状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置活动状态")]
    public async Task<bool> SetStatus(UserInput input)
    {
        var row = await _BrandActivityService.SetStatus(input);
        return row > 0;
    }

    /// <summary>
    /// 活动详情
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("活动详情")]
    public async Task<BrandActivity> GetActivityDetail(long id)
    {
        return await _BrandActivityService.GetDetail(id);
    }

    /// <summary>
    /// 取消活动
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("取消活动")]
    public async Task<bool> SetClose(BaseIdInput input)
    {
        var row = await _BrandActivityService.SetClose(input);
        return row > 0;
    }

    /// <summary>
    /// 名单列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取名单分页列表")]
    public async Task<SqlSugarPagedList<PageBrandActivityDataOutput>> PageRecordList(PageBrandActivityDataInput input)
    {
        return await _BrandActivityService.PageRecordList(input);
    }

    /// <summary>
    /// 后台活动签到
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("后台活动签到")]
    public async Task<bool> SignIn(BaseIdInput input)
    {
        return await _BrandActivityService.SignInByAdmin(input);
    }

    /// <summary>
    /// 批量导入添加用户
    /// </summary>
    /// <param name="file"></param>
    /// <param name="actId"></param>
    /// <returns></returns>
    [DisplayName("批量导入添加用户")]
    public async Task<ActivityResultExportOutput> UploadFile([Required] IFormFile file, [FromQuery] long actId)
    {
        var ret = await _BrandActivityService.UploadFile(file,actId);

        return ret;
    }


    #region 活动审核
    /// <summary>
    /// 活动审核
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("活动审核")]
    public async Task<bool> ActivityReview(ActivityDataReview input)
    {
        if (!await _BrandActivityService.ActivityReview(input))
            return false;

        var activityInfo = await _BrandActivityService.GetDetail(input.ActivityId);
        var userInfo = await _sysUserService.GetUserInfo(input.UserId);
        var orderInfo = await _BrandActivityService.orderinfo(input);

        //审核拒绝
        if (!input.ReviewStatus)
        {
            if (orderInfo == null)
                return false;

            if (orderInfo.OrderAmount > 0)
            {
                if (!await ProcessOrderRefund(input.UserId, orderInfo))
                    return false;
            }
        }
        else //审核通过，如果配置了致趣百川的会议id，向致趣百川同步用户报名数据
        {
            //如果配置了致趣百川的会议id&活动需要审核，向致趣百川同步用户报名数据
            if (activityInfo.ZQMeetingId.HasValue && activityInfo.NeedReview)
            {
                await _BrandActivityService.ActBesChannelSync(activityInfo.ZQMeetingId.Value, orderInfo, userInfo);
            }
            //审核通过的，直接同步到线下课学员表
            {
                await Task.Run(async () =>
                {
                    await App.GetService<IOfflineCourseService>().SyncOfflineCourseUser(new List<long>() { input.UserId }, null, input.ActivityId);
                });                
            }
        }
        await Task.Run(async () =>
        {
            try
            {
                await SendNotificationSms(input.ReviewStatus, activityInfo, userInfo);
            }
            catch { }
        });
        return true;
    }

    [NonAction]
    private async Task<bool> ProcessOrderRefund(long userId, BrandActivityOrder orderInfo)
    {
        var orderInput = new OrderRefundInput
        {
            CreateUserId = userId,
            OrderId = orderInfo.Id,
            RefundAmount = orderInfo.OrderAmount,
            RefundType = CourseRefundTypeEnum.RefundAndCourse
        };

        return await _BrandActivityService.rejectOrderRefund(orderInput);
    }

    [NonAction]
    private async Task SendNotificationSms(bool isApproved, BrandActivity activityInfo, UserInfo userInfo)
    {
        var templateCode = isApproved ?
        SmsTemplateCodeEnum.活动审核通过通知 :
        SmsTemplateCodeEnum.活动审核未通过通知;

        var template = await _sysSmsTemplateService.GetInfo((int)templateCode);

        var smsLog = new SysSmsSendLog
        {
            Type = isApproved ? SmsSendTypeEnum.activityConfirm : SmsSendTypeEnum.activityRejection,
            PhoneNumber = userInfo.Phone,
            TemplateCode = template.TemplateCode,
            TemplateParam = JSON.Serialize(new
            {
                name = string.IsNullOrWhiteSpace(userInfo.RealName) ? userInfo.NickName : userInfo.RealName,
                name1 = activityInfo.ActivityName
            })
        };

        AlibabaCloudSms.SendSms(smsLog);
    }

    #endregion

    /// <summary>
    /// 导出活动名单列表
    /// </summary>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Export"), NonUnify]
    [DisplayName("导出活动名单列表")]
    public async Task<IActionResult> ExportShop(PageBrandActivityDataInput input)
    {
        var act = await _BrandActivityService.GetDetail(input.ActivityId);
        var exportList = await _BrandActivityService.GetExportList(input);

        var excelExporter = new ExcelExporter();

        var res = await excelExporter.ExportAsByteArray(exportList);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") { FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + $"{act.ActivityName}报名信息.xlsx" };
    }

    /// <summary>
    /// 活动短链接
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("活动短链接")]
    public async Task<string> ActivityShortUrl(ShortUrlInput input)
    {
        return await _noahShortHelper.GetShortUrl(input.Url);
    }

    /// <summary>
    /// 获取致趣百川会议分类列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取致趣百川会议分类列表")]
    //[AllowAnonymous]
    public async Task<List<BesChannelsCateOutput>> GetCateList()
    {
        //await _besChannelsHelper.MemberList(new BesChannelsMemberListInput()
        //{
        //    Identity ="1"
        //});
        //await _besChannelsHelper.MemberInfo(new BesChannelsMemberInfoInput()
        //{
        //    Mobile = "17788214502"
        //});
        return await _besChannelsHelper.GetCateList();
    }

    /// <summary>
    /// 获取致趣百川会议列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取致趣百川会议列表")]
    public async Task<List<BesChannelsMeetingOutput>> GetMeetingList(int cateId)
    {
        var ret = await _besChannelsHelper.GetMeettingList(cateId);
        return ret;
    }
    #endregion

    #region 订单管理

    /// <summary>
    /// 获取活动订单分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取活动订单分页列表")]
    public async Task<SqlSugarPagedList<PageActivityOrderOutput>> Page(PageActivityOrderInput input)
    {
        return await _BrandActivityService.Page(input);
    }

    /// <summary>
    /// 获取活动订单详情
    /// </summary>
    /// <param name="OrderId"></param>
    /// <returns></returns>
    [DisplayName("获取活动订单详情")]
    public async Task<ActivityOrderDetailOutput> GetOrderDetail(long OrderId)
    {
        return await _BrandActivityService.Detail(OrderId);
    }

    /// <summary>
    /// 活动订单退款申请
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("活动订单退款申请")]
    public async Task<bool> OrderRefundSubmit(OrderRefundInput input)
    {
        input.CreateUserId = _userManager.UserId;
        return await _BrandActivityService.OrderRefund(input);
    }

    /// <summary>
    /// 获取售后记录列表
    /// </summary>
    /// <param name="orderId"></param>
    /// <returns></returns>
    [DisplayName("获取售后记录列表")]
    public async Task<List<BrandActivityOrderRefund>> GetOrderRefundList(long orderId)
    {
        return await _BrandActivityService.GetOrderRefundList(orderId);
    }
    #endregion
}
