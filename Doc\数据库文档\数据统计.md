# 圈子数据统计数据库设计文档

[toc]

## 统计汇总表(StatisticsSummary)

| 序号 | 列名 | 类型 | 说明 |  
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | CircleId | bigint | 内容id |
| 3 | Date | datetime | 日期 |
| 4 | Views | int | 曝光量 |
| 5 | Clicks | int | 点击量 |
| 6 | VisitorsViews | int | 访客曝光数 |
| 7 | VisitorsClicks | int | 访客点击数 |


## 点击量明细表(StatisticsClicks)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | CircleId | bigint | 内容id |
| 3 | CreateTime | datetime | 点击时间 |
| 4 | UserId | bigint | 用户Id |
| 5 | UserAgent | nvarchar(500) | UserAgent |


## 曝光量明细表(StatisticsViews)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | CircleId | bigint | 内容id | 
| 3 | CreateTime | datetime | 曝光时间 |
| 4 | UserId | bigint | 用户Id |
| 5 | UserAgent | nvarchar(500) | UserAgent |


## 访客点击明细表(StatisticsVisitorsClicks)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | CircleId | bigint | 内容id |
| 3 | CreateTime | datetime | 点击时间 |
| 4 | UserId | bigint | 用户Id |


## 访客曝光明细表(StatisticsVisitorsViews)

| 序号 | 列名 | 类型 | 说明 |  
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | CircleId | bigint | 内容id |
| 3 | CreateTime | datetime | 访问时间 |
| 4 | UserId | bigint | 用户Id |


## 粉丝关注表(UserFans)

| 序号 | 列名 | 类型 | 说明 |
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | AuthorId | bigint | 作者id |
| 3 | UserId | bigint | 粉丝id |
| 4 | Date | datetime | 日期 |
| 5 | CreateTime | datetime | 创建时间 |


## 关注记录表(UserFollows)

| 序号 | 列名 | 类型 | 说明 | 
|-|-|-|-|
| 1 | Id | bigint | Id |
| 2 | AuthorId | bigint | 作者id |
| 3 | IsFollow | bit | 是否关注 |
| 4 | Date | datetime | 日期 |
| 5 | CreateTime | datetime | 创建时间 |
| 6 | UserId | bigint | 用户Id |
