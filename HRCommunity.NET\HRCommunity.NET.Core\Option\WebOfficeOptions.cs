namespace HRCommunity.NET.Core;

/// <summary>
/// WebOffice 在线预览配置选项
/// </summary>
public sealed class WebOfficeOptions : IConfigurableOptions
{
    /// <summary>
    /// 是否启用 WebOffice 在线预览
    /// </summary>
    public bool IsEnable { get; set; } = true;

    /// <summary>
    /// 自定义域名（用于 WebOffice 预览）
    /// </summary>
    public string CustomDomain { get; set; } = string.Empty;

    /// <summary>
    /// 默认签名URL过期时间（秒）
    /// </summary>
    public int DefaultExpireTime { get; set; } = 3600;

    /// <summary>
    /// 支持的文件格式
    /// </summary>
    public List<string> SupportedFormats { get; set; } = new();

    /// <summary>
    /// 默认水印配置
    /// </summary>
    public WebOfficeWatermarkOptions DefaultWatermark { get; set; } = new();

    /// <summary>
    /// 默认权限配置
    /// </summary>
    public WebOfficePermissionOptions DefaultPermissions { get; set; } = new();
}

/// <summary>
/// WebOffice 水印配置
/// </summary>
public class WebOfficeWatermarkOptions
{
    /// <summary>
    /// 水印文字（Base64URL编码）
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 水印字号
    /// </summary>
    public int Size { get; set; } = 30;

    /// <summary>
    /// 水印透明度（0-100）
    /// </summary>
    public int Transparency { get; set; } = 60;

    /// <summary>
    /// 水印颜色
    /// </summary>
    public string Color { get; set; } = "#000000";

    /// <summary>
    /// 水印旋转角度（0-360）
    /// </summary>
    public int Rotate { get; set; } = 0;

    /// <summary>
    /// 水印字体（Base64URL编码）
    /// </summary>
    public string? FontType { get; set; }
}

/// <summary>
/// WebOffice 权限配置
/// </summary>
public class WebOfficePermissionOptions
{
    /// <summary>
    /// 是否允许打印
    /// </summary>
    public bool AllowPrint { get; set; } = true;

    /// <summary>
    /// 是否允许复制
    /// </summary>
    public bool AllowCopy { get; set; } = true;

    /// <summary>
    /// 是否允许导出为PDF
    /// </summary>
    public bool AllowExport { get; set; } = true;
}
